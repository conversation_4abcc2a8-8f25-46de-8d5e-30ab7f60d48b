{"paper_id": "18980380", "_pdf_hash": "ffe93b67a395cc51d6dc4c5f438a6bbc08a3f31a", "abstract": [{"section": "Abstract", "text": "This technical note studies Markov decision processes under parameter uncertainty. We adapt the distributionally robust optimization framework, assume that the uncertain parameters are random variables following an unknown distribution, and seek the strategy which maximizes the expected performance under the most adversarial distribution. In particular, we generalize a previous study [1] which concentrates on distribution sets with very special structure to a considerably more generic class of distribution sets, and show that the optimal strategy can be obtained efficiently under mild technical conditions. This significantly extends the applicability of distributionally robust MDPs by incorporating probabilistic information of uncertainty in a more flexible way.", "cite_spans": [], "ref_spans": []}, {"section": "Abstract", "text": "Index Terms-Distributional robustness, Markov decision processes, parameter uncertainty.", "cite_spans": [], "ref_spans": []}], "body_text": [{"section": "", "text": ". Illustration of the confidence sets.", "cite_spans": [], "ref_spans": []}, {"section": "", "text": "optimizing variable and ξ is the unknown parameter, distributionally robust optimization solves max x∈X [inf μ∈C E ξ∼μ u(x, ξ)], where C is an a priori known set of distributions.", "cite_spans": [], "ref_spans": []}, {"section": "", "text": "We highlight our contributions by comparing with [1] . In [1] the state-wise ambiguity set is restricted to the following form:C s = {μ s |μ s (O i s ) ≥ α i s ∀ i = 1, . . . , n s }, where α i s ≤ α j s and O i s is a proper set of uncertain parameters with a \"nested-set\" structure, i.e., satisfying O i s ⊆ O j s , for all i < j [see Fig. 1(a) ]. This setup can effectively model distributions with a single mode (such as a Gaussian distribution), but less so when modeling multi-mode distributions such as a mixture Gaussian distribution. Moreover, other probabilistic information such as mean, variance etc. cannot be incorporated. Thus, in this technical note, we extend the distributionally robust MDP approach to handle ambiguity sets with more general structures. In particular, we consider a class of ambiguity sets, first proposed in [18] as a unifying framework for modeling and solving distributionally robust single-stage optimization problems, and embed them into the distributionally robust MDPs setup. These ambiguity sets are considerably more general: they are characterized by a class of O i s which can either be nested or disjoint [as shown in Fig. 1(b) ], and moreover, additional linear constraints are allowed to define the ambiguity set, which can be used to incorporate probabilistic information such as mean, covariance or other variation measures. We show that, under this more general class of ambiguity sets, the resulting distributionally robust MDPs remain tractable under mild technical conditions, and often outperform previous methods thanks to the fact that it can model uncertainty in a more flexible way.", "cite_spans": [{"start": 49, "end": 52, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 58, "end": 61, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 845, "end": 849, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [{"start": 337, "end": 346, "text": "Fig. 1(a)", "ref_id": "FIGREF2"}, {"start": 1166, "end": 1175, "text": "Fig. 1(b)", "ref_id": "FIGREF2"}]}, {"section": "II. PRELIMINARIES", "text": "Throughout the technical note, we use capital letters to denote matrices, and bold face letters to denote column vectors. We use e i (m) to denote the ith elementary vector of length m, and use R n + to denote the nonnegative orthant of R n . If C is the set of joint probability distributions of three random vectors a, b, and c, then (a,b) C denotes the set of marginal distributions of (a, b). We use ⊕ to represent mixture distribution: given two probability distributions F 1 , F 2 and a <PERSON><PERSON><PERSON> random variable x which takes value 1 w.p. p, xF 1 ⊕ (1 − x)F 2 is a random variable such that it follows distribution F 1 w.p. p, and follows F 2 w.p. 1 − p. We use N (m, σ 2 ) to represent a Gaussian distribution with mean m and variance σ 2 .", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "A (finite) Markov Decision Process (MDP) is defined as a 6-tuple T, γ, S, A, p, r . Here, T is the (possibly infinite) decision horizon; 0018-9286 © 2015 IEEE. Personal use is permitted, but republication/redistribution requires IEEE permission.", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "See http://www.ieee.org/publications_standards/publications/rights/index.html for more information.", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "γ ∈ (0, 1] is the discount factor; S is the state set and A s is the action set of state s ∈ S, both assumed to be finite. The parameter p and r are the transition probability and the expected reward, respectively. That is, for s ∈ S and a ∈ A s , r(s, a) is the expected reward and p(s |s, a) is the probability that the next state is s . Following [2] , we denote the set of all history-dependent randomized strategies by Π HR . We use subscript s to denote the value associated with the state s: e.g., r s denotes the vector form of the rewards associated with the state s, and π s is the (randomized) action chosen at state s for strategy π.", "cite_spans": [{"start": 350, "end": 353, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "The elements in the vector p s are listed in the following way: the transition probabilities of the same action are arranged in the same block, and inside each block they are listed according to the order of the next state. We use s to denote the (random) state following s, and Δ(s) to denote the probability simplex on A s . We use to represent Cartesian product, e.g., p = s∈S p s . For a given strategy π ∈ Π HR , we denote the expected (discounted) total-reward under parameters pair (p, r) as u(π, p, r)", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "A Distributionally Ambiguous MDP (DAMDP) is defined as a tuple T, γ, S, A,C S , where the transition probability p and the expected reward r are unknown. Instead, they are assumed to obey a joint distribution μ 0 (also unknown) that belongs to a known ambiguity set", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "While the DAMDP framework can be very general, mostC S result in formulations that are computationally intractable (e.g., [1] , [19] ). Hence, we make the following requirement ofC S such that the parameters among different states are independent.", "cite_spans": [{"start": 122, "end": 125, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 128, "end": 132, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "Assumption 1: The ambiguity setC S has the following property:", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "where \"state-wise ambiguity set\"C s is a set of distributions of parameters of state s. By the definition ofC S , the state-wise property applies to C S as well. This property is the same as the concept of \"s-rectangularity\" in [16] , and is essential for reducing DAMDP to robust MDP in Lemma 1. In addition, [20] showed that the robust MDP with coupled uncertainty sets is computationally challenging, which implies solving DAMDP with nonrectangular ambiguity sets is even harder.", "cite_spans": [{"start": 228, "end": 232, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 310, "end": 314, "text": "[20]", "ref_id": "BIBREF19"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "We now discuss the admissible state-wise ambiguity set. Our formulation of the state-wise ambiguity set follows the unifying framework of [18] . In specific, given s ∈ S, the state-wise ambiguity set is representable with the following standard form:", "cite_spans": [{"start": 138, "end": 142, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "are the lower and upper bounds of the probability that parameters belong to a confidence set. Thus, each confidence set O i s provides an estimation of the uncertain parameters pair (p s , r s ,ũ s ) subject to a different confidence level. Ambiguity setsC s contain prescribed conic representable confidence sets and mean values residing on an affine manifold, which is rich enough to encompass and extend several ambiguity sets considered in recent literature (e.g., [1] , [19] , [21] ). The set of joint distribution of (p s , r s ) is hence C s Δ = (ps ,rs)C s . Notice that a classical technique called \"lifting\" is used here: We introduce an auxiliary random vectorũ, so that some non-linear relationship can be modeled linearly. For example, a constraint on the variance can be modeled using this standard form (see [22, Example 2] ), which is otherwise impossible without the auxiliary variable. This lifting technique thus allows us to model a rich variety of structural information about the marginal distribution of (p, r) in a unified manner. Note when the ambiguity set only contains the support of random variables, i.e.,", "cite_spans": [{"start": 469, "end": 472, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 475, "end": 479, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 482, "end": 486, "text": "[21]", "ref_id": "BIBREF21"}, {"start": 823, "end": 827, "text": "[22,", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "where the a-priori information of unknown parameters is that they belong to an uncertainty set.", "cite_spans": [], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "Assumptions 2 to 4 are standard requirements for the confidence sets, proposed in [18] . The first one asserts the relationship between different confidence sets.", "cite_spans": [{"start": 82, "end": 86, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": []}, {"section": "II. PRELIMINARIES", "text": "The nesting condition is illustrated in Fig. 1(b) . Next, for any s ∈ S we require thatC s satisfies the following regularity condition.", "cite_spans": [], "ref_spans": [{"start": 40, "end": 49, "text": "Fig. 1(b)", "ref_id": "FIGREF2"}]}, {"section": "Assumption 3 (Regularity Conditions forC s ):", "text": "1) The confidence set O ns s is bounded and has probability one, that is, ", "cite_spans": [], "ref_spans": []}, {"section": "Assumption 3 (Regularity Conditions forC s ):", "text": "s are proper cones (i.e., a closed, convex and pointed cone with nonempty interior).", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "This section focuses on DAMDP with a finite number of decision stages. We show that a strategy defined through backward induction, which we call S-robust strategy, is distributionally robust. We further show such a strategy is solvable in polynomial time under mild technical conditions. This generalizes results in [1] to a significantly more general class of ambiguity sets.", "cite_spans": [{"start": 316, "end": 319, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Similar to [10] , we assume that when a state is visited multiple times, each time it can take a different parameter realization (nonstationary model). This assumption is justified mainly because the stationary model is generally intractable and a lower-bound of it is given by the non-stationary model. Therefore, multiple visits to a state can be treated as visiting different states. By introducing dummy states as in [1, Assumption 2.2], for finite horizon DAMDP we make the following assumption without loss of generality. This will simplify our exposition.", "cite_spans": [{"start": 11, "end": 15, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Assumption 5: 1) Each state belongs to only one stage.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "2) The terminal reward equals zero.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "3) The first stage only contains one state s ini .", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Using the condition 1 of Assumption 5, we partition S according to the stage each state belongs to. That is, we let S t be the set of states belong to tth stage.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "For π ∈ Π HR and μ ∈ C S , we denote the expected performance of a DAMDP as w π, μ, (s ini ) Δ = E (p,r)∼μ {u(π, p, r)} = u(π, p, r)dμ(p, r).", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "In words, each strategy is evaluated by its expected performance under the (respective) most adversarial distribution of the uncertain parameters, and a distributionally robust strategy is the optimal strategy according to this metric. The main focus of this section is deriving approaches to solve the distributionally robust strategy. To this end, we need the following definition.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Definition 2: Given a DAMDP T, γ, S, A,C S , we define the Srobust strategy as follows", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "3) A strategyπ * is a Srobust strategy if ∀ s ∈ S, and every history h that ends at s, we haveπ * s , conditioned on history h, is a S-robust action.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "The definition requires that the strategy must be robust w.r.t. each sub-problem, and hence the name \"S-robust.\" The following theorem shows any S-robust strategy π * is distributionally robust, and is the main result of this technical note.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Theorem 1: Let T < ∞. Under Assumptions 1, 2, 4, and 5, if π * is a S-robust strategy, then 1) π * is a distributionally robust strategy with respect to C S . 2) There exists μ * ∈ C s such that (π * , μ * ) is a saddle point. That is", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Proof: We first state a Lemma from [1, Lemma 3.2] without proof.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Lemma 1: Under Assumption 1, fix π ∈ Π HR and μ ∈ C S , denote p = E μ (p) and r = E μ (r). We have w(π, μ, (s ini )) = u(π, p, r).", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Lemma 1 means for any strategy, the expected performance under an admissible distribution μ only depends on the expected value of parameters under μ. Thus, the distributionally robust MDPs reduce to robust MDPs. Next we characterize the set of expected value of the parameters.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Lemma 2: For s ∈ S and π s ∈ Δ(s), we define the set Z s = {E μs (p s , r s )|μ s ∈ C s }. Then set Z s is convex and compact.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Proof: First, we show that, for s ∈ S and π s ∈ Δ(s), the set defined asZ s = {E μs (p s , r s ,ũ s )|μ s ∈C s } is convex and compact. The convexity can be easily shown, which is omitted due to space constraints (see [22] for details). To show the compactness, notice thatC s is weakly closed (i.e., closed w.r.t. to the weak topology) since the feasible set of each of constraint is weakly closed which implies their intersection is also weakly closed. Thus,Z s is closed since it is the image ofC s under expectation (which is a continuous function). This impliesZ s is compact since O ns s is bounded and henceZ s is bounded. Finally, since Z s is the projection onto the first two coordinates of set Z s , its convexity and compactness thus follow.", "cite_spans": [{"start": 218, "end": 222, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Lemma 2 implies that, for s ∈ S and π s ∈ Δ(s), there exists (p * s , r * s ) ∈ Z s that satisfies inf (ps,rs)∈Zs u(π s , p s , r s ) = u(π s , p * s , r * s ). Since saddle point of the minimax objective exists for robust MDPs (e.g., [10] , [11] ), we can complete the proof of part 2) following a similar procedure as the last portion of proof for [1, Theorem 3.1]. We omit the details due to space constraint (see [22] for details). Part 1) then follows part 2) immediately.", "cite_spans": [{"start": 235, "end": 239, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 242, "end": 246, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 417, "end": 421, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "We now investigate the computational aspect of finding the S-robust action.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Theorem 2: Under Assumption 2, 3, 4, and 5, for s ∈ S t where t < T , the S-robust action is the optimal solution of the following optimization problem (termed Srobust problem hereafter): Proof: The proof essentially follows from [18] and duality of convex optimization [23] , and can be found in the longer version [22] of this technical note.", "cite_spans": [{"start": 230, "end": 234, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 270, "end": 274, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 316, "end": 320, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "Thus, since for s ∈ S t , Δ(s) is compact, we can solve the S-robust action in polynomial time if all K i s are \"easy\" cones such as linear, conic quadratic or semidefinite cones. Moreover, using Theorem 1, by backward induction, we can obtain the S-robust strategy efficiently.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "By virtue of the lifting technique [18, <PERSON><PERSON> 5], we show below several widely used ambiguity sets are indeed special cases ofC s defined in (1) . We further derive their corresponding S-robust problems. See [22] for additional examples (variance and expected Huber loss function). ", "cite_spans": [{"start": 143, "end": 146, "text": "(1)", "ref_id": "BIBREF0"}, {"start": 210, "end": 214, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "This example can also be treated via \"classical\" robust optimization by virtue of Lemma 1.", "cite_spans": [], "ref_spans": []}, {"section": "III. FINITE HORIZON DISTRIBUTIONALLY ROBUST MDPS", "text": "The finite horizon DAMDP can be easily extended to discountedreward infinite horizon setup. We can generalize the notion of S-robust strategy, which turns to be distributionally robust in both stationary and non-stationary models. This extension is similar to [1] and can be found in [22] .", "cite_spans": [{"start": 260, "end": 263, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 284, "end": 288, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": []}, {"section": "IV. SIMULATION", "text": "In this section, we study two synthetic numerical examples: a machine replacement problem and a path planning problem. In the machine replacement problem, the reward parameters are uncertain; whereas in the path planning problem, the transition probabilities are uncertain. All results were generated on desktop with Intel Core i5-3570 CPU of 3.40 GHz clock speed and 8 GB RAM. The S-robust problems are solved in Matlab using the CVX package [24] .", "cite_spans": [{"start": 443, "end": 447, "text": "[24]", "ref_id": "BIBREF24"}], "ref_spans": []}, {"section": "<PERSON><PERSON> Uncertainty in the Machine Replacement Problem", "text": "We consider a machine replacement problem similar to the one in [12] . Consider the repair cost incurred by a factory that holds a large number of machines, given that each of these machines is modeled with a same underlying MDP for which rewards are subject to uncertainty.", "cite_spans": [{"start": 64, "end": 68, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": []}, {"section": "1) Machine Replacement as a MDP With Gaussian Rewards:", "text": "We first consider a machine replacement problem with 50 states, 2 actions (\"repair\" and \"not repair\") for each state, deterministic transitions, a discount factor of 0.8, and uncertain rewards following Gaussian distributions independently [see Fig. 2(a) ]: For the first 48 states, the \"repair\" action has a cost N (130, 1) . The 49th and 50th states of the machine's life are designed to be risky: not repairing at state 50 incurs a highly uncertain cost N (100, 800), while repairing at both states is a more secure but still uncertain option with a cost N (130, 10) . The detailed implementation is as follows: We use the mean value of uncertain rewards to compute the nominal strategy. For both robust and distributionally robust strategy, we construct confidence sets usinĝ m ± 3σ for the first 49 states, andm ± 4σ for state 50 wherem andσ 2 are mean and variance estimated from samples (see [22] for details), as it is more risky and thus hard to estimate. In addition, we construct an extra confidence set (centered at the mean) with 60%-70% confidence level (i.e., α 1 50 = 0.6, α 1 50 = 0.7) for distributionally robust strategy. The optimal paths followed by three strategies are shown in Fig. 2(a) .", "cite_spans": [{"start": 899, "end": 903, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": [{"start": 245, "end": 254, "text": "Fig. 2(a)", "ref_id": "FIGREF3"}, {"start": 309, "end": 324, "text": "cost N (130, 1)", "ref_id": "FIGREF1"}, {"start": 553, "end": 569, "text": "cost N (130, 10)", "ref_id": "FIGREF1"}, {"start": 1201, "end": 1210, "text": "Fig. 2(a)", "ref_id": "FIGREF3"}]}, {"section": "1) Machine Replacement as a MDP With Gaussian Rewards:", "text": "The performance of the strategies obtained by using the nominal, the robust and the distributionally robust approaches is presented in Fig. 3 . The corresponding average total discounted rewards and computational times are shown in Table I . The nominal strategy results in the highest average total discounted rewards. This is well expected as we are using the exact mean value of the reward as the nominal Fig. 2 . Two instances of a machine replacement problem. Fig. 2(a) shows Gaussian uncertainty in the rewards, while Fig. 2(b) shows mixed Gaussian uncertainty in the rewards. parameter. However, the nominal strategy is highly risky: it cannot prevent bad performance (e.g., −0.025) from happening, which is undesirable. While the nominal strategy, blind to any form of risk, finds no advantage in ever repairing, the robust strategy ends up following a highly conservative policy (repairing the machine at state 49 to avoid state 50). In contrast, the distributionally robust optimal strategy makes use of more distributional information and handles the risk efficiently by waiting until state 50 and then repair the machine. Therefore, this strategy beats the nominal and robust strategies in that it strikes a good tradeoff between high mean reward and low variance over 10,000 different trials. These results coincide with what one would typically expect from the three solution concepts. Fig. 4 . Illustration of the confidence sets for two distributionally robust strategies.", "cite_spans": [], "ref_spans": [{"start": 135, "end": 141, "text": "Fig. 3", "ref_id": "FIGREF1"}, {"start": 232, "end": 239, "text": "Table I", "ref_id": "TABREF0"}, {"start": 408, "end": 414, "text": "Fig. 2", "ref_id": "FIGREF3"}, {"start": 465, "end": 474, "text": "Fig. 2(a)", "ref_id": "FIGREF3"}, {"start": 524, "end": 533, "text": "Fig. 2(b)", "ref_id": "FIGREF3"}, {"start": 1400, "end": 1406, "text": "Fig. 4", "ref_id": "FIGREF0"}]}, {"section": "2) Machine Replacement as a MDP With Mixed Gaussian Rewards:", "text": "The second experiment has a similar setup as the previous one, except that not repairing at the 50th state has a reward which follows a mixed Gaussian distribution [see Fig. 2(b) ]. This experiment illustrates the effect of the two different nested-set structures shown in Fig. 1 . In specific, we apply the two different distributionally robust approaches (proposed in [1] and this technical note respectively), and show that our method outperforms. The detailed implementation is as follows: For the robust and two distributionally robust strategies, we construct uncertainty set corresponding to 99% probability support of the rewards for the first 49 states, and 99.9% for the 50th state that is more risky, using estimated mean and variance (see [22] for details). For the first distributionally robust strategy proposed in [1] , we construct two additional nested confidence sets O 1 50 and O 2 50 [see Fig. 4(a) ], which w.p. 40%-50% and 60%-70% respectively the uncertain rewards belong to. In contrast, for the second distributionally robust strategy proposed in this technical note, we construct two disjoint confidence sets O 1 50 and O 2 50 [see Fig. 4 (b)] with 70%-80% and 0%-10% confidence level, respectively. Specifically, we select these two intervals around the peaks of the two Gaussian elements [i.e., N (100, 10) and N (140, 2)] to better model this mixed distribution. The optimal paths followed for the three strategies are shown in Fig. 2(b) .", "cite_spans": [{"start": 370, "end": 373, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 751, "end": 755, "text": "[22]", "ref_id": "BIBREF22"}, {"start": 829, "end": 832, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 169, "end": 178, "text": "Fig. 2(b)", "ref_id": "FIGREF3"}, {"start": 273, "end": 279, "text": "Fig. 1", "ref_id": "FIGREF2"}, {"start": 909, "end": 918, "text": "Fig. 4(a)", "ref_id": "FIGREF0"}, {"start": 1158, "end": 1164, "text": "Fig. 4", "ref_id": "FIGREF0"}, {"start": 1457, "end": 1466, "text": "Fig. 2(b)", "ref_id": "FIGREF3"}]}, {"section": "2) Machine Replacement as a MDP With Mixed Gaussian Rewards:", "text": "The performance of the three strategies obtained is presented in Fig. 5 . The corresponding average total discounted rewards and computational times are shown in Table II . As expected, the robust strategy ends up following a highly conservative policy repairing the machine at state 49 to avoid state 50. The first distributionally robust strategy, not modeling the mixture Gaussian distribution well, finds it advantageous to repair at the 50th state. In contrast, capable of capturing the distribution information in a more flexible way, the second distributionally robust strategy better models the uncertainty and finds not repairing the machine at state 50 is optimal. The performance comparison clearly shows the second distributionally robust strategy is more desirable, which highlights the distributionally robust approach with general structure of confidence sets can be beneficial in practice.", "cite_spans": [], "ref_spans": [{"start": 65, "end": 71, "text": "Fig. 5", "ref_id": "FIGREF5"}, {"start": 162, "end": 170, "text": "Table II", "ref_id": "TABREF0"}]}, {"section": "2) Machine Replacement as a MDP With Mixed Gaussian Rewards:", "text": "We remark that, in practice, one can obtain the modality structure of uncertain parameters in a data-driven way by applying clustering algorithms to an initial primitive data set. For example, one may check the histogram of historical observations. If the data concentrates on several distinct and disjoint bins, our multi-model DAMDP approach can be applied. Moreover, we note that networked control systems (NCSs) have recently emerged as a topic of significant interest in the control community. A typical application of NCSs is in modern [25] and [26] proposed a novel two-layer structure to solve the setpoints compensation problem for industrial processes under network-based environment.", "cite_spans": [{"start": 542, "end": 546, "text": "[25]", "ref_id": "BIBREF25"}, {"start": 551, "end": 555, "text": "[26]", "ref_id": "BIBREF26"}], "ref_spans": []}, {"section": "B. Transition Uncertainty in the Path Planning Problem", "text": "We now consider a path planning problem, similar to the one presented in [1] : an agent wants to exit a 4 × 21 maze [shown in Fig. 6(a) ] using the least possible time. Starting from the upper-left corner, the agent can move up, down, left and right, but can only exit the grid at the lower-right corner. Here, a white box stands for a normal place where the agent needs one time unit to pass through. A shaded box represents a \"shaky\" place: if an agent reaches a \"shaky\" place, then he may risk jumping to the starting point (\"reboot\"). The true transition probability of the jump follows a distribution", "cite_spans": [{"start": 73, "end": 76, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 126, "end": 135, "text": "Fig. 6(a)", "ref_id": "FIGREF6"}]}, {"section": "B. Transition Uncertainty in the Path Planning Problem", "text": "The four approaches are implemented as follows: The nominal approach neglects this random jump. The robust approach takes a worst-case analysis, i.e., it assumes that with 30%, the whole probability support of transition, the agent will jump to the spot with the highest costto-go. The first distributionally robust approach takes into account an additional information by using two nested confidence sets: the jump probability parameter belonging to 9%-11% is of a confidence 1 − λ. The second distributionally robust approach, which is proposed in this technical note, incorporates more information. In specific, we construct an extra confidence interval disjoint with the above 9%-11% interval. It states that the chance of jumping with probability 20% is λ.", "cite_spans": [], "ref_spans": []}, {"section": "B. Transition Uncertainty in the Path Planning Problem", "text": "The performance of strategies of the nominal, the robust and the two distributionally robust approaches is shown in Fig. 6(b) , where the error bars show the standard error of the expected time to exit. The CPU times of computing optimal policies for four strategies are 0.461, 549, 642, and 654 seconds, respectively. The second distributionally robust approach achieves the best performance over virtually the whole spectrum of λ. This is well expected, since additional probabilistic Fig. 6(a) illustrates the maze for the path plawnning problem. Fig. 6(b) shows the performance comparisons between nominal, robust and two distributionally robust strategies over 3,000 runs of the path planning problem.", "cite_spans": [], "ref_spans": [{"start": 116, "end": 125, "text": "Fig. 6(b)", "ref_id": "FIGREF6"}, {"start": 487, "end": 496, "text": "Fig. 6(a)", "ref_id": "FIGREF6"}, {"start": 550, "end": 559, "text": "Fig. 6(b)", "ref_id": "FIGREF6"}]}, {"section": "B. Transition Uncertainty in the Path Planning Problem", "text": "information is available to and incorporated by the second distributionally robust approach which considers ambiguity sets with more general structures.", "cite_spans": [], "ref_spans": []}, {"section": "V. CONCLUSION", "text": "In this technical note, we considered Markov decision problems with uncertainty. Specifically, we generalized the distributionally robust approach proposed in [1] to incorporate more general ambiguity sets proposed in [18] to model a-priori probabilistic information of the uncertain parameters. We proposed a way to compute the distributionally robust strategy through a Bellman type backward induction. We showed that the strategy, which achieves maximum expected utility under the worst admissible distributions of uncertain parameters, can be solved in polynomial time under some mild technical conditions. We believe that many important problems that are usually addressed using standard MDP models could be revisited and better resolved using the proposed models when parameter uncertainty exists, as this formulation naturally enables the decision maker to account for more general parameter uncertainty.", "cite_spans": [{"start": 159, "end": 162, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 218, "end": 222, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": []}], "bib_entries": {"BIBREF0": {"title": "Distributionally robust Markov decision processes", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Math. Oper. Res", "link": "7229756"}, "BIBREF1": {"title": "Markov Decision Processes: Discrete Stochastic Dynamic Programming", "authors": [{"first": "M", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "link": "********"}, "BIBREF2": {"title": "Neuro-dynamic programming (optimization and neural computation series, 3)", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["N"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1996, "venue": "Athena Scientific", "link": null}, "BIBREF3": {"title": "Reinforcement Learning: An Introduction", "authors": [{"first": "A", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1998, "venue": "", "link": "9166388"}, "BIBREF4": {"title": "Bias and variance approximation in value function estimates", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Sun", "suffix": ""}, {"first": "J", "middle": ["N"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "Manag. Sci", "link": "10603007"}, "BIBREF5": {"title": "Convex programming with set-inclusive constraints and applications to inexact linear programming", "authors": [{"first": "A", "middle": ["L"], "last": "Soyster", "suffix": ""}], "year": 1973, "venue": "Oper. Res", "link": null}, "BIBREF6": {"title": "Robust solutions of uncertain linear programs", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1999, "venue": "Oper. Res. Lett", "link": "2474018"}, "BIBREF7": {"title": "The price of robustness", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>m", "suffix": ""}], "year": 2004, "venue": "Oper. Res", "link": "8946639"}, "BIBREF8": {"title": "Robust Optimization", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["El"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "link": null}, "BIBREF9": {"title": "Robust control of Markov decision processes with uncertain transition matrices", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["El"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2005, "venue": "Oper. Res", "link": "1537485"}, "BIBREF10": {"title": "Robust dynamic programming", "authors": [{"first": "G", "middle": ["N"], "last": "Iyengar", "suffix": ""}], "year": 2005, "venue": "Math. Oper. Res", "link": "710328"}, "BIBREF11": {"title": "Percentile optimization for Markov decision processes with parameter uncertainty", "authors": [{"first": "E", "middle": [], "last": "Delage", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "Oper. Res", "link": "10308849"}, "BIBREF12": {"title": "Markov decision processes with imprecise transition probabilities", "authors": [{"first": "C", "middle": ["C"], "last": "White", "suffix": ""}, {"first": "H", "middle": ["K"], "last": "Eldeib", "suffix": ""}], "year": 1994, "venue": "Oper. Res", "link": "207242061"}, "BIBREF13": {"title": "Solving uncertain <PERSON><PERSON> decision problems", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2001, "venue": "", "link": "59762877"}, "BIBREF14": {"title": "Learning under ambiguity", "authors": [{"first": "L", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2007, "venue": "Rev. Econ. Studies", "link": "15546892"}, "BIBREF15": {"title": "<PERSON><PERSON> decision processes", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Math. Oper. Res", "link": "6103434"}, "BIBREF16": {"title": "The robustness-performance tradeoff in Markov decision processes", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "Proc. NIPS", "link": "63859912"}, "BIBREF17": {"title": "Distributionally robust convex optimization", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>m", "suffix": ""}], "year": 2014, "venue": "Oper. Res", "link": "16625241"}, "BIBREF18": {"title": "Distributionally robust optimization under moment uncertainty with application to data-driven problems", "authors": [{"first": "E", "middle": [], "last": "Delage", "suffix": ""}, {"first": "Y", "middle": [], "last": "Ye", "suffix": ""}], "year": 2010, "venue": "Oper. Res", "link": null}, "BIBREF19": {"title": "Lightning does not strike twice: Robust MDPs with coupled uncertainty", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2012, "venue": "Proc. 29th Int. Conf. Machine Learning (ICML'12)", "link": "486400"}, "BIBREF20": {"title": "Available", "authors": [], "year": "", "venue": "", "link": null}, "BIBREF21": {"title": "Distributionally robust joint chance constraints with second-order moment information", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Math. Programm", "link": "11547182"}, "BIBREF22": {"title": "Distributionally robust counterpart in Markov decision processes", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "", "link": "18980380"}, "BIBREF23": {"title": "Convex Optimization", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>denberghe", "suffix": ""}], "year": 2004, "venue": "", "link": "37925315"}, "BIBREF24": {"title": "CVX: Matlab Software for Disciplined Convex Programming, Version 2.1", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "link": null}, "BIBREF25": {"title": "A combined adaptive neural network and nonlinear model predictive control for multirate networked industrial process control", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "IEEE Trans. Neural Netw. Learn. Syst", "link": "18576331"}, "BIBREF26": {"title": "Networked multirate output feedback control for setpoints compensation and its application to rougher flotation process", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Fan", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "IEEE Trans. Ind. Electron", "link": "24341930"}}, "ref_entries": {"FIGREF0": {"text": "The condition 1 of Assumption 3 ensures the confidence set with largest index, O ns s , contains the support of the joint unknown parameters pair (p s , r s ,ũ s ). The second condition stipulates that there is a probability distribution μ s (p s , r s ,ũ s ) ∈C s that satisfies the probability bounds in (1) as strict inequalities whenever the corresponding probability interval [α i s , α i s ] is non-degenerate. For each individual O i s , we make the following assumption to ensure tractability.Assumption For s ∈ S, i ∈ I s , each nonempty and convex confidence set O i s is defined as", "type": "figure"}, "FIGREF1": {"text": "Here, K i s * represents the cone dual to K i s ; set A(i) Δ = {i} ∪ {i ∈ I s : O i s O i s };ṽ t+1 is the vector form ofṽ t+1 (s ) for all s ∈ S t+1 ; and V s Δ = [e 1 (|A s |)ṽ t+1 , . . . , e |As| (|A s |)ṽ t+1 ] .", "type": "figure"}, "FIGREF2": {"text": "Mean Absolute Deviation): Assume that E rs∼μs (rs) [|r s − m|] ≤ f for m, f ∈ R |As| . [18] shows thatC s , which involves the auxiliary random vectorũ s ∈ R |As| , can be expressed asC s = {μ s (r s ,ũ s )|Eũ s ∼μs [ũ s ] = f , μ s (ũ s ≥ r s − m,ũ s ≥ m − r s ) = 1}. Note that μ s (r s ) ∈ rsC s . In this case Problem (3) can be rewritten as minimize w,πs ,κ,ν w subject to κ − f ν ≤ w κ + p sṼs π s + m π s ≥ 0 π s ∈ Δ(s), ν ≥ 0.", "type": "figure"}, "FIGREF3": {"text": "Mean): Assume that we only know a noisy empirical estimator of the exact mean of p s . That is, given G ∈ R M ×(|As|×|s|) , f ∈ R M and p s ∼ μ s (p s ), GE ps∼μs(ps) [p s ] K f , where K is a proper cone. [18] shows thatC s , which involves the auxiliary random vectorũ s ∈ R M , can be expressed asC s = {μ s (p s , u s )|Eũ s ∼μs [ũ s ] = f , μ s (Gp s Kũs ) = 1}. Note that μ s (p s ) ∈ psC s . Problem (3) now takes the form minimize w,πs ,κ,ν", "type": "figure"}, "FIGREF4": {"text": "Performance comparisons between nominal, robust, and distributionally robust strategies on 10,000 runs of the machine replacement problem with Gaussian rewards (The bottom figure focuses on the interval [ −0.0045, −0.001]).", "type": "figure"}, "FIGREF5": {"text": "Performance comparisons between robust and two distributionally robust strategies on 10,000 runs of the machine replacement problem with mixed Gaussian rewards.", "type": "figure"}, "FIGREF6": {"text": "Fig. 6. Fig. 6(a) illustrates the maze for the path plawnning problem. Fig. 6(b) shows the performance comparisons between nominal, robust and two distributionally robust strategies over 3,000 runs of the path planning problem.", "type": "figure"}, "TABREF0": {"text": "TOTAL DISCOUNTED REWARDS AND COMPUTATIONAL TIMES OF NOMINAL, ROBUST, AND DISTRIBUTIONALLY ROBUST STRATEGIES IN MACHINE REPLACEMENT PROBLEM WITH GAUSSIAN REWARDS", "type": "table"}, "TABREF1": {"text": "TOTAL DISCOUNTED REWARDS AND COMPUTATION<PERSON> TIMES OF ROBUST AND TWO DISTRIBUTIONALLY ROBUST STRATEGIES IN MACHINE REPLACEMENT PROBLEM WITH MIXED GAUSSIAN REWARDS industrial systems, in which the components are often connected over network media. Our multi-model DAMDP approach might be extended for network-based performance tracking control of complex industrial processes, where recent work", "type": "table"}}}