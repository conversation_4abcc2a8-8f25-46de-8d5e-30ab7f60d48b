{"title": "Écoles d'été: Méthodes numériques", "mathematical_content": {"greek_letters": {"epsilon": "ε", "sigma": "σ", "rho": "ρ", "pi": "π", "alpha": "α", "beta": "β"}, "operators": {"infinity": "∞", "element_of": "∈", "not_equal": "≠", "less_equal": "≤", "greater_equal": "≥", "much_less": "≪", "arrow": "→", "square_root": "√"}, "equations": ["dX^ε_t = (1/ε)f(X^ε_t, Y^ε_t)dt + √(2σ_x)dW_x(t)", "ρ^x_∞ when X^ε_t = x", "∫_0^∞ f(x)dx = π/2"]}, "french_text": {"accented_words": ["développement", "méthode", "équation", "thé<PERSON>e", "problème", "phén<PERSON>ène"], "sample_sentence": "L'étude des équations différentielles nécessite une approche théorique et numérique."}, "metadata": {"created": "2025-06-24T17:19:17.235282", "encoding_info": "This file contains Unicode characters"}}