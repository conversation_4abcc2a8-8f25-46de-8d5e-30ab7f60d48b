#-------------------- resource directories ---------------------
# properties of where to find directories necessary for GROBID
# EACH KEY REFERENCING A PATH HAS TO ENDS WITH ".path"
grobid.resource.path=./resources
grobid.temp.path=./tmp
grobid.bin.path=./bin

#-------------------- external/native libs ---------------------
#path to folder containing native libraries of 3rd parties
grobid.nativelibrary.path=./lib
grobid.3rdparty.pdf2xml.path=./pdf2xml
grobid.3rdparty.pdf2xml.memory.limit.mb=6096
grobid.3rdparty.pdf2xml.timeout.sec=60
#-------------------------------------------------------------

#-------------------- consolidation --------------------
# Define the bibliographical data consolidation service to be used, either "crossref" for CrossRef REST API or "glutton" for https://github.com/kermitt2/biblio-glutton
grobid.consolidation.service=crossref
#grobid.consolidation.service=glutton
#org.grobid.glutton.host=cloud.science-miner.com/glutton
#org.grobid.glutton.port=0
org.grobid.glutton.host=localhost
org.grobid.glutton.port=8070
#org.grobid.crossref.mailto=<EMAIL>
#org.grobid.crossref.token=yourmysteriouscrossrefmetadataplusauthorizationtokentobeputhere

#-------------------- proxy --------------------
#proxy to be used for external call to the crossref REST API service or Glutton service if not deployed under proxy ("null" when no proxy)
grobid.proxy_host=null
grobid.proxy_port=null
#------------------------------------------------------

#-------------------- runtime ------------------
grobid.crf.engine=wapiti
#grobid.crf.engine=delft
#grobid.crf.engine=crfpp
grobid.delft.install=../delft
grobid.delft.useELMo=false
grobid.pdf.blocks.max=100000
grobid.pdf.tokens.max=1000000

#-------------------- training ------------------
#number of threads for training the wapiti models (0 to use all available processors)
grobid.nb_threads=0

#-------------------- language identification  ------------------
#property for using or not the language identifier (true|false)
grobid.use_language_id=true
grobid.language_detector_factory=org.grobid.core.lang.impl.CybozuLanguageDetectorFactory
#determines if properties like the firstnames, lastnames country codes and dictionaries are supposed to be read from $GROBID_HOME path or not (possible values (true|false) dafault is false)
grobid.resources.inHome=true
#------------------------------------------------------

#-------------------- pooling -------------------
# Maximum parallel connections allowed
org.grobid.max.connections=72
# Maximum time wait to get a connection when the pool is full (in seconds)
org.grobid.pool.max.wait=1
#------------------------------------------------------
