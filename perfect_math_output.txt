and \( \varepsilon \) is a small parameter encoding the timescale separation between the processes \( X^{\varepsilon} \) and \( Y^{\varepsilon} \). The parameters \( \sigma_x \in \mathbb{R}^{m \times m} \), \( \sigma_y \in \mathbb{R}^{n \times n} \) are constant matrices, and \( W_x \), \( W_y \) are independent \( m \)- and \( n \)-dimensional Brownian motions, respectively. In order for this system to have a well-defined limit as \( \varepsilon \to 0 \), it is assumed for all \( x \in \mathbb{R}^m \) that the equation for \( Y^\varepsilon_t \) admits a unique invariant measure \( \rho_x^\infty \) when \( X_t^\varepsilon = x \) is taken as a fixed parameter, and that the slow drift \( f(x, \cdot) \) has mean 0 with respect to \( \rho_x^\infty \). When \( \varepsilon \ll 1 \), traditional methods for the numerical solution of SDEs are not suitable for this problem, because the time step required to perform a stable and accurate time integration would be prohibitively small. It is well-known, however, that the slow process \( X^\varepsilon \) converges weakly in \( C([0, T]) \), in the limit as \( \varepsilon \to 0 \), to the solution of a simpler effective equation.