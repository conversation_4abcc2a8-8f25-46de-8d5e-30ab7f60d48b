#!/usr/bin/env python3
"""
Simple and reliable JSON to LaTeX converter for GROBID output.
Focuses on clean, working conversion without over-complication.
"""

import json
import re
import argparse
from typing import Dict, List, Any, Optional

class SimpleJSONToLaTeX:
    def __init__(self):
        # Basic Unicode to LaTeX mapping
        self.unicode_map = {
            # Greek letters
            '\u03b1': r'\alpha',
            '\u03b2': r'\beta', 
            '\u03b3': r'\gamma',
            '\u03b4': r'\delta',
            '\u03b5': r'\epsilon',
            '\u03c3': r'\sigma',
            '\u03c6': r'\phi',
            '\u03d5': r'\phi',  # alternative phi
            
            # Mathematical operators
            '\u2202': r'\partial',
            '\u2207': r'\nabla',
            '\u2212': r'-',
            '\u00b1': r'\pm',
            '\u00d7': r'\times',
            '\u221a': r'\sqrt',
            '\u221e': r'\infty',
            
            # Superscripts (common ones)
            '\u207b\u00b9': r'^{-1}',
            '\u22121': r'^{-1}',
            '\u207b\u00b2': r'^{-2}',
            
            # Bullets and symbols
            '\u2022': r'$\bullet$ ',
            '\u2219': r'$\bullet$ ',
            '\u2026': r'\ldots',
        }
    
    def clean_unicode(self, text: str) -> str:
        """Convert Unicode escape sequences and characters to LaTeX."""
        # Handle Unicode escape sequences
        try:
            text = text.encode().decode('unicode_escape')
        except:
            pass
        
        # Apply Unicode mappings
        for unicode_char, latex_equiv in self.unicode_map.items():
            text = text.replace(unicode_char, latex_equiv)
        
        return text
    
    def fix_math_notation(self, text: str) -> str:
        """Fix mathematical notation patterns."""
        # Fix subscripts: "q t" -> "q_t", "X epsilon t" -> "X_{\epsilon t}"
        text = re.sub(r'([a-zA-Z])\s+\\epsilon\s+([a-zA-Z0-9])', r'\1_{\\epsilon \2}', text)
        text = re.sub(r'([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', text)
        
        # Fix differential notation: "dq t" -> "dq_t", "dW t" -> "dW_t"
        text = re.sub(r'd([A-Z])\s+([a-zA-Z0-9])', r'd\1_{\2}', text)
        
        # Add proper spacing around dt, dW
        text = re.sub(r'\bdt\b', r'\\, dt', text)
        text = re.sub(r'\bdW', r'\\, dW', text)
        
        # Fix nabla subscripts
        text = re.sub(r'\\nabla_\{([a-zA-Z])\}', r'\\nabla_\1', text)
        
        # Add space between Greek letters and variables
        text = re.sub(r'(\\gamma|\\beta|\\alpha)([A-Z])', r'\1 \2', text)
        
        return text
    
    def escape_latex_chars(self, text: str) -> str:
        """Escape special LaTeX characters."""
        # Don't escape if already in math mode or LaTeX command
        if '\\' in text and any(cmd in text for cmd in ['\\alpha', '\\beta', '\\gamma', '\\nabla']):
            return text
        
        # Basic escaping for non-math text
        replacements = {
            '&': r'\&',
            '%': r'\%',
            '#': r'\#',
        }
        
        for char, escaped in replacements.items():
            text = text.replace(char, escaped)
        
        return text
    
    def process_text(self, text: str) -> str:
        """Process text through all cleaning steps."""
        if not text:
            return ""
        
        text = self.clean_unicode(text)
        text = self.fix_math_notation(text)
        text = self.escape_latex_chars(text)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def format_equation(self, eq_text: str, eq_num: str = "") -> str:
        """Format equation with proper LaTeX structure."""
        clean_text = self.process_text(eq_text)
        
        if eq_num:
            return f"""\\begin{{equation}}
{clean_text}
\\label{{eq:{eq_num}}}
\\end{{equation}}"""
        else:
            return f"""\\begin{{equation}}
{clean_text}
\\end{{equation}}"""
    
    def convert_json_to_latex(self, json_file: str, output_file: str):
        """Convert GROBID JSON to LaTeX."""
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract basic info
        title = data.get('title', 'Untitled Document')
        abstract = data.get('abstract', '')
        
        # Start LaTeX document
        latex_content = [
            r'\documentclass[10pt]{article}',
            r'\usepackage[utf8]{inputenc}',
            r'\usepackage{amsmath,amssymb,mathtools}',
            r'\usepackage{geometry}',
            r'\usepackage{hyperref}',
            r'\usepackage{graphicx}',
            r'',
            r'\geometry{margin=1in}',
            r'',
            r'\begin{document}',
            r'',
            f'\\title{{{self.process_text(title)}}}',
            r'\maketitle',
            r'',
        ]
        
        # Add abstract if present
        if abstract:
            latex_content.extend([
                r'\begin{abstract}',
                self.process_text(abstract),
                r'\end{abstract}',
                r'',
            ])
        
        # Process body text
        body_text = data.get('pdf_parse', {}).get('body_text', [])
        current_section = None
        
        for item in body_text:
            text = item.get('text', '')
            section = item.get('section', '')
            eq_spans = item.get('eq_spans', [])
            
            # Handle section changes
            if section and section != current_section:
                current_section = section
                latex_content.append(f'\\section{{{self.process_text(section)}}}')
                latex_content.append('')
            
            # Handle equations
            if eq_spans:
                for eq_span in eq_spans:
                    eq_text = eq_span.get('raw_str', text)
                    eq_num = eq_span.get('eq_num', '')
                    latex_content.append(self.format_equation(eq_text, eq_num))
                    latex_content.append('')
            elif text.strip() and text.strip() != "EQUATION":
                # Regular text
                processed_text = self.process_text(text)
                if processed_text:
                    latex_content.append(processed_text)
                    latex_content.append('')
        
        # End document
        latex_content.append(r'\end{document}')
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(latex_content))
        
        print(f"Converted {json_file} to {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Convert GROBID JSON to LaTeX')
    parser.add_argument('input_file', help='Input JSON file')
    parser.add_argument('-o', '--output', help='Output LaTeX file', required=True)
    
    args = parser.parse_args()
    
    converter = SimpleJSONToLaTeX()
    converter.convert_json_to_latex(args.input_file, args.output)

if __name__ == "__main__":
    main()
