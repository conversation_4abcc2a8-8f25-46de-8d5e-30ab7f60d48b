import os
import sys
import json
import re
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# Add the s2orc-doc2json path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 's2orc-doc2json'))

from doc2json.grobid2json.grobid.grobid_client import GrobidClient
from doc2json.grobid2json.process_pdf import process_pdf_file

def clean_math_content(text: str) -> str:
    """Clean and format mathematical content for LaTeX"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Handle common mathematical symbols and expressions
    replacements = {
        '≤': r'\leq', '≥': r'\geq', '≠': r'\neq', '±': r'\pm', '∞': r'\infty',
        '∑': r'\sum', '∏': r'\prod', '∫': r'\int', '∂': r'\partial', '∇': r'\nabla',
        '√': r'\sqrt', 'α': r'\alpha', 'β': r'\beta', 'γ': r'\gamma', 'δ': r'\delta',
        'ε': r'\epsilon', 'θ': r'\theta', 'λ': r'\lambda', 'μ': r'\mu', 'π': r'\pi',
        'σ': r'\sigma', 'τ': r'\tau', 'φ': r'\phi', 'ψ': r'\psi', 'ω': r'\omega',
        'Γ': r'\Gamma', 'Δ': r'\Delta', 'Θ': r'\Theta', 'Λ': r'\Lambda',
        'Π': r'\Pi', 'Σ': r'\Sigma', 'Φ': r'\Phi', 'Ψ': r'\Psi', 'Ω': r'\Omega',
        '→': r'\to', '←': r'\leftarrow', '↔': r'\leftrightarrow', '⇒': r'\Rightarrow',
        '⇐': r'\Leftarrow', '⇔': r'\Leftrightarrow', '∈': r'\in', '∉': r'\notin',
        '⊂': r'\subset', '⊃': r'\supset', '⊆': r'\subseteq', '⊇': r'\supseteq',
        '∪': r'\cup', '∩': r'\cap', '∅': r'\emptyset', '×': r'\times', '·': r'\cdot',
    }
    
    for unicode_char, latex_cmd in replacements.items():
        text = text.replace(unicode_char, latex_cmd)
    
    return text

def extract_formulas_from_tei(soup: BeautifulSoup) -> List[Dict]:
    """Extract mathematical formulas from TEI XML"""
    formulas = []
    
    # Find all formula elements
    formula_elements = soup.find_all('formula')
    
    for formula in formula_elements:
        formula_data = {
            'id': formula.get('xml:id', ''),
            'type': 'display' if formula.find('label') else 'inline',
            'content': '',
            'latex': '',
            'label': ''
        }
        
        # Get label if present
        label_elem = formula.find('label')
        if label_elem:
            formula_data['label'] = label_elem.get_text().strip('()')
        
        # Get text content and clean it
        formula_data['content'] = clean_math_content(formula.get_text())
        
        formulas.append(formula_data)
    
    return formulas

def grobid_pdf_to_latex_improved(pdf_path: str, output_path: str) -> str:
    """Convert PDF to LaTeX using GROBID with improved content handling"""
    
    print(f"Processing PDF: {pdf_path}")
    
    # Create temporary directories
    temp_dir = "temp_dir"
    output_dir = "output_dir"
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Process PDF with GROBID (reuse existing if available)
        paper_id = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
        json_file = os.path.join(output_dir, f'{paper_id}.json')
        
        if not os.path.exists(json_file):
            print("Processing PDF with GROBID...")
            json_file = process_pdf_file(pdf_path, temp_dir, output_dir)
        else:
            print("Using existing GROBID output...")
        
        # Also get the TEI XML file for formula extraction
        tei_file = os.path.join(temp_dir, f'{paper_id}.tei.xml')
        
        # Parse TEI XML for mathematical content
        formulas = []
        if os.path.exists(tei_file):
            print("Extracting mathematical formulas from TEI XML...")
            with open(tei_file, 'r', encoding='utf-8') as f:
                tei_content = f.read()
            soup = BeautifulSoup(tei_content, 'xml')
            formulas = extract_formulas_from_tei(soup)
            print(f"Found {len(formulas)} mathematical formulas")
        
        # Load the JSON output
        with open(json_file, 'r', encoding='utf-8') as f:
            paper_data = json.load(f)
        
        # Start building LaTeX document
        latex_content = []
        
        # Document class and packages
        latex_content.extend([
            "\\documentclass[11pt]{article}",
            "\\usepackage[utf8]{inputenc}",
            "\\usepackage[T1]{fontenc}",
            "\\usepackage{amsmath}",
            "\\usepackage{amsfonts}",
            "\\usepackage{amssymb}",
            "\\usepackage{mathtools}",
            "\\usepackage{geometry}",
            "\\usepackage{graphicx}",
            "\\usepackage{hyperref}",
            "\\usepackage{cite}",
            "\\usepackage[french,english]{babel}",
            "",
            "\\geometry{margin=1in}",
            "",
            "\\begin{document}",
            ""
        ])
        
        # Title and metadata
        if 'title' in paper_data and paper_data['title']:
            title = clean_math_content(paper_data['title'])
            latex_content.append(f"\\title{{{title}}}")
        
        if 'authors' in paper_data and paper_data['authors']:
            authors = []
            for author in paper_data['authors']:
                if 'first' in author and 'last' in author:
                    authors.append(f"{author['first']} {author['last']}")
                elif 'name' in author:
                    authors.append(author['name'])
            if authors:
                author_string = ' \\and '.join(authors)
                latex_content.append(f"\\author{{{author_string}}}")
        
        latex_content.extend([
            "\\maketitle",
            ""
        ])
        
        # Abstract
        if 'abstract' in paper_data and paper_data['abstract']:
            latex_content.append("\\begin{abstract}")
            if isinstance(paper_data['abstract'], str):
                abstract_text = clean_math_content(paper_data['abstract'])
                latex_content.append(abstract_text)
            elif isinstance(paper_data['abstract'], list):
                for abs_section in paper_data['abstract']:
                    if isinstance(abs_section, dict) and 'text' in abs_section:
                        abstract_text = clean_math_content(abs_section['text'])
                        latex_content.append(abstract_text)
                    elif isinstance(abs_section, str):
                        abstract_text = clean_math_content(abs_section)
                        latex_content.append(abstract_text)
            latex_content.extend(["\\end{abstract}", ""])
        
        # Continue processing body text
        if 'body_text' in paper_data and paper_data['body_text']:
            print(f"Processing {len(paper_data['body_text'])} body text sections...")
            current_section = None
            for i, section in enumerate(paper_data['body_text']):
                # Handle section headers
                if 'section' in section and section['section'] != current_section:
                    current_section = section['section']
                    section_title = clean_math_content(section['section'])
                    if section_title and section_title.strip() and len(section_title.strip()) > 2:
                        # Determine section level
                        if 'sec_num' in section and section['sec_num']:
                            sec_num = str(section['sec_num'])
                            if '.' in sec_num:
                                latex_content.append(f"\\subsection{{{section_title}}}")
                            else:
                                latex_content.append(f"\\section{{{section_title}}}")
                        else:
                            latex_content.append(f"\\section{{{section_title}}}")
                        latex_content.append("")

                if 'text' in section:
                    section_text = clean_math_content(section['text'])

                    # Handle equations marked as "EQUATION" in the text
                    if section_text.strip() == "EQUATION":
                        if 'eq_spans' in section and section['eq_spans']:
                            for eq_span in section['eq_spans']:
                                if 'raw_str' in eq_span:
                                    eq_content = clean_math_content(eq_span['raw_str'])
                                    if 'eq_num' in eq_span and eq_span['eq_num']:
                                        eq_label = eq_span['eq_num'].strip('()')
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            f"\\label{{eq:{eq_label}}}",
                                            "\\end{equation}",
                                            ""
                                        ])
                                    else:
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            "\\end{equation}",
                                            ""
                                        ])
                        continue

                    # Replace inline equation references with proper LaTeX
                    if 'eq_spans' in section and section['eq_spans']:
                        for eq_span in section['eq_spans']:
                            if 'raw_str' in eq_span and 'text' in eq_span:
                                eq_content = clean_math_content(eq_span['raw_str'])
                                # Replace the placeholder text with inline math
                                section_text = section_text.replace(eq_span['text'], f"${eq_content}$")

                    # Skip empty or very short text
                    if section_text.strip() and len(section_text.strip()) > 5:
                        latex_content.append(section_text)
                        latex_content.append("")

        # Add bibliography if available
        if 'bib_entries' in paper_data and paper_data['bib_entries']:
            latex_content.extend([
                "\\begin{thebibliography}{99}",
                ""
            ])

            for bib_id, bib_entry in paper_data['bib_entries'].items():
                bib_text = ""
                if 'title' in bib_entry and bib_entry['title']:
                    bib_text += f"\\textit{{{clean_math_content(bib_entry['title'])}}}"
                if 'authors' in bib_entry and bib_entry['authors']:
                    authors = []
                    for author in bib_entry['authors']:
                        if 'first' in author and 'last' in author:
                            authors.append(f"{author['first']} {author['last']}")
                        elif 'name' in author:
                            authors.append(author['name'])
                    if authors:
                        bib_text = f"{', '.join(authors)}. {bib_text}"

                if 'venue' in bib_entry and bib_entry['venue']:
                    bib_text += f". {clean_math_content(bib_entry['venue'])}"
                if 'year' in bib_entry and bib_entry['year']:
                    bib_text += f" ({bib_entry['year']})"

                if bib_text.strip():
                    latex_content.append(f"\\bibitem{{{bib_id}}} {bib_text}")

            latex_content.extend([
                "",
                "\\end{thebibliography}",
                ""
            ])

        # End document
        latex_content.append("\\end{document}")

        return latex_content
        
    except Exception as e:
        print(f"Error processing PDF with GROBID: {e}")
        raise

def main():
    """Main function to process the PDF"""
    pdf_path = 'mcf_analytical_presentation.pdf'
    output_path = 'mcf_analytical_presentation_improved.tex'
    
    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        return
    
    try:
        latex_content = grobid_pdf_to_latex_improved(pdf_path, output_path)
                # Handle section headers
                if 'section' in section and section['section'] != current_section:
                    current_section = section['section']
                    section_title = clean_math_content(section['section'])
                    if section_title and section_title.strip() and len(section_title.strip()) > 2:
                        # Determine section level
                        if 'sec_num' in section and section['sec_num']:
                            sec_num = str(section['sec_num'])
                            if '.' in sec_num:
                                latex_content.append(f"\\subsection{{{section_title}}}")
                            else:
                                latex_content.append(f"\\section{{{section_title}}}")
                        else:
                            latex_content.append(f"\\section{{{section_title}}}")
                        latex_content.append("")
                
                if 'text' in section:
                    section_text = clean_math_content(section['text'])
                    
                    # Handle equations marked as "EQUATION" in the text
                    if section_text.strip() == "EQUATION":
                        if 'eq_spans' in section and section['eq_spans']:
                            for eq_span in section['eq_spans']:
                                if 'raw_str' in eq_span:
                                    eq_content = clean_math_content(eq_span['raw_str'])
                                    if 'eq_num' in eq_span and eq_span['eq_num']:
                                        eq_label = eq_span['eq_num'].strip('()')
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            f"\\label{{eq:{eq_label}}}",
                                            "\\end{equation}",
                                            ""
                                        ])
                                    else:
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            "\\end{equation}",
                                            ""
                                        ])
                        continue

                    # Replace inline equation references with proper LaTeX
                    if 'eq_spans' in section and section['eq_spans']:
                        for eq_span in section['eq_spans']:
                            if 'raw_str' in eq_span and 'text' in eq_span:
                                eq_content = clean_math_content(eq_span['raw_str'])
                                # Replace the placeholder text with inline math
                                section_text = section_text.replace(eq_span['text'], f"${eq_content}$")
                    
                    # Skip empty or very short text
                    if section_text.strip() and len(section_text.strip()) > 5:
                        latex_content.append(section_text)
                        latex_content.append("")
        
        # Add bibliography if available
        if 'bib_entries' in paper_data and paper_data['bib_entries']:
            latex_content.extend([
                "\\begin{thebibliography}{99}",
                ""
            ])
            
            for bib_id, bib_entry in paper_data['bib_entries'].items():
                bib_text = ""
                if 'title' in bib_entry and bib_entry['title']:
                    bib_text += f"\\textit{{{clean_math_content(bib_entry['title'])}}}"
                if 'authors' in bib_entry and bib_entry['authors']:
                    authors = []
                    for author in bib_entry['authors']:
                        if 'first' in author and 'last' in author:
                            authors.append(f"{author['first']} {author['last']}")
                        elif 'name' in author:
                            authors.append(author['name'])
                    if authors:
                        bib_text = f"{', '.join(authors)}. {bib_text}"
                
                if 'venue' in bib_entry and bib_entry['venue']:
                    bib_text += f". {clean_math_content(bib_entry['venue'])}"
                if 'year' in bib_entry and bib_entry['year']:
                    bib_text += f" ({bib_entry['year']})"
                
                if bib_text.strip():
                    latex_content.append(f"\\bibitem{{{bib_id}}} {bib_text}")
            
            latex_content.extend([
                "",
                "\\end{thebibliography}",
                ""
            ])
        
        # End document
        latex_content.append("\\end{document}")
        
        # Write to output file
        final_latex = "\n".join(latex_content)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_latex)
        
        print(f"LaTeX file created: {output_path}")
        print(f"Found and processed {len(formulas)} mathematical formulas")
        
        # Display statistics
        lines = len(latex_content)
        equations = final_latex.count('\\begin{equation}')
        inline_math = final_latex.count('$') // 2
        sections = final_latex.count('\\section{') + final_latex.count('\\subsection{')
        
        print(f"\nDocument statistics:")
        print(f"- Total lines: {lines}")
        print(f"- Sections: {sections}")
        print(f"- Display equations: {equations}")
        print(f"- Inline math expressions: {inline_math}")
        
        return output_path
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
