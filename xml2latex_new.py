# ##
# # Basic usage (output file will be automatically named)
# python xml2latex_new.py mcf_analytical_presentation_original.tei.xml

# # Specify custom output filename
# python xml2latex_new.py mcf_analytical_presentation_original.tei.xml -o converted_output.tex

# # Get help
# python xml2latex_new.py -h




import os
import sys
import re
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import argparse

# TEI & MathML 命名空间
NS = {
    'tei': 'http://www.tei-c.org/ns/1.0',
    'm': 'http://www.w3.org/1998/Math/MathML'
}

def clean_text(text):
    """Clean and normalize text content"""
    if text is None:
        return ""
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Handle special LaTeX characters
    special_chars = {
        '&': '\\&',
        '%': '\\%',
        '$': '\\$',
        '#': '\\#',
        '_': '\\_',
        '{': '\\{',
        '}': '\\}',
        '~': '\\textasciitilde{}',
        '^': '\\textasciicircum{}',
        '\\': '\\textbackslash{}',
        '<': '\\textless{}',
        '>': '\\textgreater{}'
    }
    # Only replace if not already escaped
    for char, replacement in special_chars.items():
        if char not in ['\\', '$']:  # Skip backslash and dollar sign for now
            text = text.replace(char, replacement)
    
    # Handle non-ASCII characters
    text = text.replace('é', "\\'{e}")
    text = text.replace('è', "\\`{e}")
    text = text.replace('ê', "\\^{e}")
    text = text.replace('ë', '\\"e')
    text = text.replace('à', "\\`{a}")
    text = text.replace('â', "\\^{a}")
    text = text.replace('ç', "\\c{c}")
    text = text.replace('ï', '\\"i')
    
    return text.strip()

def format_math_formula(formula_text):
    """Format mathematical formula text for LaTeX"""
    if formula_text is None:
        return ""
    
    text = formula_text.strip()
    # Clean up common OCR issues in math formulas
    replacements = {
        # Differential operators
        "dX t": "dX_t",
        "dY t": "dY_t",
        "dXt": "dX_t",
        "dYt": "dY_t",
        # Subscripts
        "X t": "X_t",
        "Y t": "Y_t",
        "Xt": "X_t",
        "Yt": "Y_t",
        # Function notation
        "f (": "f(",
        "g (": "g(",
        "F (": "F(",
        "A (": "A(",
        "W (": "W(",
        "L0": "L_0",
        # Set notation
        "t Rm": "t \\in \\mathbb{R}^m",
        "t Rn": "t \\in \\mathbb{R}^n",
        "x Rm": "x \\in \\mathbb{R}^m",
        "x Rn": "x \\in \\mathbb{R}^n",
        "Rm×m": "\\mathbb{R}^{m\\times m}",
        "Rn×n": "\\mathbb{R}^{n\\times n}",
        "Rm": "\\mathbb{R}^m",
        "Rn": "\\mathbb{R}^n",
        "R^m": "\\mathbb{R}^m",
        "R^n": "\\mathbb{R}^n",
        # Common mathematical symbols
        "→": "\\rightarrow",
        "×": "\\times",
        "≠": "\\neq",
        "≤": "\\leq",
        "≥": "\\geq",
        "∈": "\\in",
        "∀": "\\forall",
        "∃": "\\exists",
        "∂": "\\partial",
        "∇": "\\nabla",
        "∑": "\\sum",
        "∞": "\\infty",
        "∫": "\\int",
        # Various mathematics symbols
        "→ 0": "\\rightarrow 0",
        "→ ∞": "\\rightarrow \\infty",
        "([0, T ])": "(C[0, T])",
        "([0,T])": "(C[0,T])",
        # Greek letters
        "α": "\\alpha",
        "β": "\\beta",
        "γ": "\\gamma",
        "Γ": "\\Gamma",
        "δ": "\\delta",
        "Δ": "\\Delta",
        "ε": "\\varepsilon",
        "ζ": "\\zeta",
        "η": "\\eta",
        "θ": "\\theta",
        "Θ": "\\Theta",
        "λ": "\\lambda",
        "Λ": "\\Lambda",
        "μ": "\\mu",
        "π": "\\pi",
        "Π": "\\Pi",
        "ρ": "\\rho",
        "σ": "\\sigma",
        "Σ": "\\Sigma",
        "τ": "\\tau",
        "φ": "\\phi",
        "Φ": "\\Phi",
        "χ": "\\chi",
        "ψ": "\\psi",
        "Ψ": "\\Psi",
        "ω": "\\omega",
        "Ω": "\\Omega"
    }
    
    for k, v in replacements.items():
        text = text.replace(k, v)
    
    # Fix spacing around operators
    text = re.sub(r'(\d)([a-zA-Z])', r'\1 \2', text)
    
    # Fix common multi-character math operators and functions
    text = re.sub(r'lim\s+([^_])', r'\\lim \1', text)
    text = re.sub(r'sup\s+', r'\\sup ', text)
    text = re.sub(r'inf\s+', r'\\inf ', text)
    text = re.sub(r'max\s+', r'\\max ', text)
    text = re.sub(r'min\s+', r'\\min ', text)
    text = re.sub(r'sin\s+', r'\\sin ', text)
    text = re.sub(r'cos\s+', r'\\cos ', text)
    text = re.sub(r'tan\s+', r'\\tan ', text)
    
    # Properly format fractions like "1/2"
    text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)
    
    # Properly handle numerical subscripts
    text = re.sub(r'([A-Za-z])(\d+)', r'\1_{\2}', text)
    
    # Ensure equation numbers are properly formatted
    text = re.sub(r'\((\d+)\)', r'\\tag{\1}', text)
    
    return text

def process_formula(formula_element):
    """Process a formula element to convert it to LaTeX math format"""
    # Extract text content
    formula_text = "".join(formula_element.itertext()).strip()
    
    # Get formula ID and label if available
    formula_id = formula_element.get("{http://www.w3.org/XML/1998/namespace}id", "")
    label_element = formula_element.find(".//tei:label", NS)
    label = label_element.text if label_element is not None else ""
    
    # Check if we have a MathML representation
    mathml = formula_element.find(".//m:math", NS)
    
    if mathml is not None:
        try:
            # Try to use mathml2latex if available
            try:
                from mathml2latex import convert as mathml2latex
                mathml_str = ET.tostring(mathml, encoding="unicode")
                formatted_formula = mathml2latex(mathml_str)
            except ImportError:
                # If mathml2latex is not available, fall back to text extraction
                formatted_formula = format_math_formula(formula_text)
        except Exception as e:
            print(f"Warning: Failed to convert MathML formula: {e}")
            formatted_formula = format_math_formula(formula_text)
    else:
        # Process text content
        formatted_formula = format_math_formula(formula_text)
    
    # Determine if this is a display equation or inline
    # Check if the formula is within a paragraph (inline) or standalone (display)
    parent = formula_element.getparent() if hasattr(formula_element, 'getparent') else None
    display_eq = True
    
    if parent is not None and parent.tag.endswith('p'):
        # Formula is inside a paragraph, likely inline
        prev_text = formula_element.tail
        if prev_text and prev_text.strip() and not prev_text.strip().endswith('.'):
            display_eq = False
    
    # Create the LaTeX formula
    if display_eq:
        if label:
            return f"\\begin{{equation}}\n{formatted_formula}\n\\label{{{formula_id}}}\n\\end{{equation}}"
        else:
            return f"\\begin{{equation*}}\n{formatted_formula}\n\\end{{equation*}}"
    else:
        return f"${formatted_formula}$"

def tei_to_latex(xml_file, output_file):
    """Convert TEI XML to LaTeX format"""
    # Parse the XML file
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
    except Exception as e:
        print(f"Error parsing XML file: {e}")
        return False
    
    # Start LaTeX document
    latex_content = [
        "\\documentclass{article}",
        "\\usepackage[utf8]{inputenc}",
        "\\usepackage{amsmath}",
        "\\usepackage{amssymb}",
        "\\usepackage{graphicx}",
        "\\usepackage{hyperref}",
        "\\title{TEI Document Converted to LaTeX}",
        "\\author{Automatic Conversion}",
        "\\date{\\today}",
        "\\begin{document}",
        "\\maketitle",
        ""
    ]
    
    # Extract the document title
    title_element = root.find(".//tei:titleStmt/tei:title[@type='main']", NS)
    if title_element is not None:
        title_text = clean_text("".join(title_element.itertext()))
        latex_content[6] = f"\\title{{{title_text}}}"
    
    # Extract author information
    authors = root.findall(".//tei:author/tei:persName", NS)
    if authors:
        author_names = []
        for author in authors:
            forename = author.find("tei:forename", NS)
            surname = author.find("tei:surname", NS)
            
            if forename is not None and surname is not None:
                name = f"{forename.text} {surname.text}"
                author_names.append(clean_text(name))
            elif surname is not None:
                author_names.append(clean_text(surname.text))
        
        if author_names:
            author_string = ' \\and '.join(author_names)
            latex_content[7] = f"\\author{{{author_string}}}"

    # Process the main text body
    body = root.find(".//tei:body", NS)
    if body is not None:
        # Process all divs in body
        process_element(body, latex_content, 0)

    # Close the LaTeX document
    latex_content.append("\\end{document}")

    # Write to output file
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("\n".join(latex_content))

    print(f"LaTeX document successfully created: {output_file}")
    return True

def process_element(element, latex_content, depth=0):
    """
    Recursively process an XML element and convert it to LaTeX
    
    Args:
        element: The XML element to process
        latex_content: The list to append LaTeX content to
        depth: The current depth (for section levels)
    """
    if element is None:
        return
    
    # Process div elements (sections)
    if element.tag == f"{{{NS['tei']}}}div":
        # Process section headers
        head = element.find("tei:head", NS)
        if head is not None:
            head_text = clean_text("".join(head.itertext()))
            if depth == 0:
                latex_content.append(f"\\section{{{head_text}}}")
            elif depth == 1:
                latex_content.append(f"\\subsection{{{head_text}}}")
            elif depth == 2:
                latex_content.append(f"\\subsubsection{{{head_text}}}")
            else:
                latex_content.append(f"\\paragraph{{{head_text}}}")
            latex_content.append("")
        
        # Process child elements
        for child in element:
            process_element(child, latex_content, depth + 1)
    
    # Process paragraphs
    elif element.tag == f"{{{NS['tei']}}}p":
        # Extract text and handle inline formulas
        para_content = []
        for child in element:
            if child.tag == f"{{{NS['tei']}}}formula":
                # Process inline formula
                formula_latex = process_formula(child)
                para_content.append(formula_latex)
            elif child.tag == f"{{{NS['tei']}}}ref":
                # Process reference
                ref_target = child.get("target", "")
                ref_text = clean_text("".join(child.itertext()))
                if ref_target.startswith("#formula_"):
                    ref_id = ref_target[1:]  # Remove the leading #
                    para_content.append(f"\\eqref{{{ref_id}}}")
                else:
                    para_content.append(ref_text)
            else:
                # Handle any other inline element
                text = clean_text("".join(child.itertext()))
                if text:
                    para_content.append(text)
        
        # Get the direct text content of the paragraph (between tags)
        if element.text:
            text = clean_text(element.text)
            para_content.insert(0, text)
        
        # Join all content and add to document
        para_text = " ".join(para_content)
        if para_text.strip():
            latex_content.append(para_text)
            latex_content.append("")
    
    # Process standalone formulas
    elif element.tag == f"{{{NS['tei']}}}formula":
        formula_latex = process_formula(element)
        latex_content.append(formula_latex)
        latex_content.append("")
    
    # Process figures
    elif element.tag == f"{{{NS['tei']}}}figure":
        # Handle figures if needed
        figure_desc = element.find(".//tei:figDesc", NS)
        if figure_desc is not None:
            desc_text = clean_text("".join(figure_desc.itertext()))
            latex_content.append(f"% Figure: {desc_text}")
            latex_content.append("")
    
    # Process tables
    elif element.tag == f"{{{NS['tei']}}}table":
        # Simple table handling
        latex_content.append("\\begin{table}[htbp]")
        latex_content.append("\\centering")
        latex_content.append("\\caption{Table}")
        latex_content.append("\\begin{tabular}{|c|c|}")
        latex_content.append("\\hline")
        
        # Process rows
        rows = element.findall(".//tei:row", NS)
        for row in rows:
            cells = row.findall(".//tei:cell", NS)
            cell_texts = [clean_text("".join(cell.itertext())) for cell in cells]
            latex_content.append(" & ".join(cell_texts) + " \\\\")
            latex_content.append("\\hline")
        
        latex_content.append("\\end{tabular}")
        latex_content.append("\\end{table}")
        latex_content.append("")
    
    # Process lists
    elif element.tag in [f"{{{NS['tei']}}}list", f"{{{NS['tei']}}}listBibl"]:
        latex_content.append("\\begin{itemize}")
        for item in element.findall(".//tei:item", NS):
            item_text = clean_text("".join(item.itertext()))
            latex_content.append(f"\\item {item_text}")
        latex_content.append("\\end{itemize}")
        latex_content.append("")
    
    # Process all other elements
    else:
        # Process children recursively
        for child in element:
            process_element(child, latex_content, depth)

def main():
    parser = argparse.ArgumentParser(description='Convert TEI XML to LaTeX with proper formula handling')
    parser.add_argument('input_file', help='Input TEI XML file path')
    parser.add_argument('-o', '--output', help='Output LaTeX file path')
    
    args = parser.parse_args()
    
    input_file = args.input_file
    output_file = args.output if args.output else os.path.splitext(input_file)[0] + ".tex"
    
    if not os.path.exists(input_file):
        print(f"Error: Input file {input_file} not found!")
        return 1
    
    success = tei_to_latex(input_file, output_file)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
