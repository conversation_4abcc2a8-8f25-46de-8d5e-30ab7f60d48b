#!/usr/bin/env python3

import os
import sys
import json
import argparse
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import subprocess

# Add the s2orc-doc2json to the path
sys.path.insert(0, 's2orc-doc2json')

class PDFToJSONConverter:
    """
    Universal PDF to JSON converter that handles documents with equations.
    Supports various document structures and multiple extraction methods.
    """
    
    def __init__(self, temp_dir="temp_dir", output_dir="output_dir"):
        self.temp_dir = temp_dir
        self.output_dir = output_dir
        self.ensure_directories()
        self.check_dependencies()
        
    def ensure_directories(self):
        """Create necessary directories if they don't exist."""
        for directory in [self.temp_dir, self.output_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"Created directory: {directory}")
    
    def check_dependencies(self):
        """Check and install required dependencies."""
        required_packages = ['PyPDF2', 'pdfplumber']
        
        for package in required_packages:
            try:
                __import__(package.lower().replace('pdf', 'PDF') if 'PDF' in package else package)
            except ImportError:
                print(f"Installing {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             capture_output=True, text=True)
    
    def extract_math_expressions(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract mathematical expressions from text using various patterns.
        Returns a list of math expressions with their positions and types.
        """
        math_expressions = []
        
        # Enhanced patterns for mathematical expressions
        patterns = [
            # LaTeX-style math
            (r'\$\$([^$]+)\$\$', 'display_latex', 'LaTeX display math'),
            (r'\$([^$\n]+)\$', 'inline_latex', 'LaTeX inline math'),
            (r'\\begin\{equation\*?\}(.*?)\\end\{equation\*?\}', 'display_equation', 'LaTeX equation'),
            (r'\\begin\{align\*?\}(.*?)\\end\{align\*?\}', 'display_align', 'LaTeX align'),
            (r'\\begin\{eqnarray\*?\}(.*?)\\end\{eqnarray\*?\}', 'display_eqnarray', 'LaTeX eqnarray'),
            (r'\\begin\{gather\*?\}(.*?)\\end\{gather\*?\}', 'display_gather', 'LaTeX gather'),
            (r'\\begin\{multline\*?\}(.*?)\\end\{multline\*?\}', 'display_multline', 'LaTeX multline'),
            
            # Common mathematical notation patterns
            (r'[a-zA-Z]\s*[=≈≡]\s*[^.!?]*[+\-*/^²³√∫∑∏]', 'inline_equation', 'Mathematical equation'),
            (r'[∫∑∏][^.!?]*d[a-zA-Z]', 'inline_integral', 'Integral/sum expression'),
            (r'[a-zA-Z][₀₁₂₃₄₅₆₇₈₉ᵢⱼₖₗₘₙₚᵣₛₜᵤᵥᵤₓᵧᵤ]+', 'subscript', 'Subscripted variable'),
            (r'[a-zA-Z][⁰¹²³⁴⁵⁶⁷⁸⁹ⁱʲᵏˡᵐⁿᵖʳˢᵗᵘᵛʷˣʸᶻ]+', 'superscript', 'Superscripted variable'),
            
            # Greek letters and mathematical symbols
            (r'[αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]', 'greek_letter', 'Greek letter'),
            (r'[∀∃∄∅∆∇∈∉∊∋∌∍∎∏∐∑−∓∔∕∖∗∘∙√∛∜∝∞∟∠∡∢∣∤∥∦∧∨∩∪∫∬∭∮∯∰∱∲∳∴∵∶∷∸∹∺∻∼∽∾∿≀≁≂≃≄≅≆≇≈≉≊≋≌≍≎≏≐≑≒≓≔≕≖≗≘≙≚≛≜≝≞≟≠≡≢≣≤≥≦≧≨≩≪≫≬≭≮≯≰≱≲≳≴≵≶≷≸≹≺≻≼≽≾≿⊀⊁⊂⊃⊄⊅⊆⊇⊈⊉⊊⊋⊌⊍⊎⊏⊐⊑⊒⊓⊔⊕⊖⊗⊘⊙⊚⊛⊜⊝⊞⊟⊠⊡⊢⊣⊤⊥⊦⊧⊨⊩⊪⊫⊬⊭⊮⊯⊰⊱⊲⊳⊴⊵⊶⊷⊸⊹⊺⊻⊼⊽⊾⊿⋀⋁⋂⋃⋄⋅⋆⋇⋈⋉⋊⋋⋌⋍⋎⋏⋐⋑⋒⋓⋔⋕⋖⋗⋘⋙⋚⋛⋜⋝⋞⋟⋠⋡⋢⋣⋤⋥⋦⋧⋨⋩⋪⋫⋬⋭⋮⋯⋰⋱⋲⋳⋴⋵⋶⋷⋸⋹⋺⋻⋼⋽⋾⋿]', 'math_symbol', 'Mathematical symbol'),
            
            # Fractions and complex expressions
            (r'\d+/\d+', 'fraction', 'Fraction'),
            (r'[a-zA-Z]+\([^)]*\)', 'function', 'Function call'),
        ]
        
        for pattern, expr_type, description in patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                content = match.group(1) if len(match.groups()) > 0 else match.group(0)
                math_expressions.append({
                    'content': content.strip(),
                    'type': expr_type,
                    'description': description,
                    'start': match.start(),
                    'end': match.end(),
                    'raw': match.group(0),
                    'confidence': self.calculate_math_confidence(content, expr_type)
                })
        
        # Remove duplicates and sort by position
        unique_expressions = {}
        for expr in math_expressions:
            key = (expr['start'], expr['end'])
            if key not in unique_expressions or expr['confidence'] > unique_expressions[key]['confidence']:
                unique_expressions[key] = expr
        
        return sorted(unique_expressions.values(), key=lambda x: x['start'])
    
    def calculate_math_confidence(self, content: str, expr_type: str) -> float:
        """Calculate confidence score for mathematical expression detection."""
        base_confidence = {
            'display_latex': 0.95,
            'inline_latex': 0.90,
            'display_equation': 0.95,
            'display_align': 0.95,
            'inline_equation': 0.80,
            'inline_integral': 0.85,
            'fraction': 0.70,
            'function': 0.60,
            'greek_letter': 0.75,
            'math_symbol': 0.80,
            'subscript': 0.65,
            'superscript': 0.65
        }.get(expr_type, 0.50)
        
        # Adjust based on content characteristics
        if len(content) < 2:
            base_confidence *= 0.7
        elif len(content) > 50:
            base_confidence *= 0.9
        
        # Boost confidence for complex expressions
        if any(symbol in content for symbol in ['∫', '∑', '∏', '√', '∞']):
            base_confidence *= 1.2
        
        return min(base_confidence, 1.0)
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common PDF artifacts
        text = re.sub(r'\uf[0-9a-f]{3}', '', text)  # Unicode private use area
        text = re.sub(r'[\x00-\x08\x0b-\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)  # Control chars
        
        return text
    
    def extract_with_grobid(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Process PDF using GROBID if available."""
        try:
            from doc2json.grobid2json.grobid.grobid_client import GrobidClient
            from doc2json.grobid2json.tei_to_json import convert_tei_xml_file_to_s2orc_json
            
            print("Processing with GROBID...")
            client = GrobidClient()
            
            paper_id = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
            tei_file = os.path.join(self.temp_dir, f'{paper_id}.tei.xml')
            
            client.process_pdf(pdf_path, self.temp_dir, "processFulltextDocument")
            
            if not os.path.exists(tei_file):
                return None
            
            paper = convert_tei_xml_file_to_s2orc_json(tei_file)
            return paper.release_json()
            
        except ImportError:
            print("GROBID not available")
            return None
        except Exception as e:
            print(f"GROBID processing failed: {str(e)}")
            return None
    
    def extract_with_pypdf2(self, pdf_path: str) -> Dict[str, Any]:
        """Extract text using PyPDF2."""
        try:
            import PyPDF2
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                pages = []
                full_text = ""
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    cleaned_text = self.clean_text(page_text)
                    
                    page_data = {
                        'page_number': page_num + 1,
                        'text': cleaned_text,
                        'math_expressions': self.extract_math_expressions(cleaned_text)
                    }
                    pages.append(page_data)
                    full_text += cleaned_text + "\n"
                
                return {
                    'extraction_method': 'PyPDF2',
                    'pages': pages,
                    'full_text': full_text,
                    'total_pages': len(pages),
                    'math_expressions': self.extract_math_expressions(full_text)
                }
                
        except ImportError:
            return None
        except Exception as e:
            print(f"PyPDF2 extraction failed: {str(e)}")
            return None
    
    def extract_with_pdfplumber(self, pdf_path: str) -> Dict[str, Any]:
        """Extract text using pdfplumber (better for tables and layout)."""
        try:
            import pdfplumber
            
            pages = []
            full_text = ""
            
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text() or ""
                    cleaned_text = self.clean_text(page_text)
                    
                    # Extract tables if any
                    tables = page.extract_tables()
                    
                    page_data = {
                        'page_number': page_num + 1,
                        'text': cleaned_text,
                        'tables': tables,
                        'math_expressions': self.extract_math_expressions(cleaned_text)
                    }
                    pages.append(page_data)
                    full_text += cleaned_text + "\n"
            
            return {
                'extraction_method': 'pdfplumber',
                'pages': pages,
                'full_text': full_text,
                'total_pages': len(pages),
                'math_expressions': self.extract_math_expressions(full_text)
            }
            
        except ImportError:
            return None
        except Exception as e:
            print(f"pdfplumber extraction failed: {str(e)}")
            return None
    
    def extract_with_pymupdf(self, pdf_path: str) -> Dict[str, Any]:
        """Extract text using PyMuPDF/fitz (good for complex layouts)."""
        try:
            import fitz  # PyMuPDF
            
            doc = fitz.open(pdf_path)
            pages = []
            full_text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text()
                cleaned_text = self.clean_text(page_text)
                
                # Get text blocks with positions
                blocks = page.get_text("dict")
                
                page_data = {
                    'page_number': page_num + 1,
                    'text': cleaned_text,
                    'blocks': blocks,
                    'math_expressions': self.extract_math_expressions(cleaned_text)
                }
                pages.append(page_data)
                full_text += cleaned_text + "\n"
            
            doc.close()
            
            return {
                'extraction_method': 'PyMuPDF',
                'pages': pages,
                'full_text': full_text,
                'total_pages': len(pages),
                'math_expressions': self.extract_math_expressions(full_text)
            }
            
        except ImportError:
            return None
        except Exception as e:
            print(f"PyMuPDF extraction failed: {str(e)}")
            return None
    
    def enhance_math_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance the extracted data with mathematical analysis."""
        all_math = data.get('math_expressions', [])
        
        # Categorize math expressions
        categories = {}
        for expr in all_math:
            expr_type = expr['type']
            if expr_type not in categories:
                categories[expr_type] = []
            categories[expr_type].append(expr)
        
        # Calculate statistics
        high_confidence = [e for e in all_math if e.get('confidence', 0) > 0.8]
        
        data['math_analysis'] = {
            'total_expressions': len(all_math),
            'high_confidence_count': len(high_confidence),
            'categories': {k: len(v) for k, v in categories.items()},
            'category_details': categories,
            'average_confidence': sum(e.get('confidence', 0) for e in all_math) / len(all_math) if all_math else 0
        }
        
        return data
    
    def convert_pdf_to_json(self, pdf_path: str, output_filename: Optional[str] = None) -> str:
        """
        Main conversion method with multiple fallback strategies.
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        print(f"Converting PDF to JSON: {pdf_path}")
        
        # Generate output filename
        if output_filename is None:
            base_name = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
            output_filename = f"{base_name}_converted.json"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        # Try multiple extraction methods in order of preference
        extraction_methods = [
            ('GROBID', self.extract_with_grobid),
            ('PyMuPDF', self.extract_with_pymupdf),
            ('pdfplumber', self.extract_with_pdfplumber),
            ('PyPDF2', self.extract_with_pypdf2),
        ]
        
        final_data = None
        
        for method_name, method_func in extraction_methods:
            print(f"Trying {method_name}...")
            try:
                result = method_func(pdf_path)
                if result:
                    print(f"Successfully extracted with {method_name}")
                    final_data = result
                    break
            except Exception as e:
                print(f"{method_name} failed: {str(e)}")
                continue
        
        if not final_data:
            raise RuntimeError("All extraction methods failed")
        
        # Enhance with mathematical analysis
        final_data = self.enhance_math_analysis(final_data)
        
        # Add metadata
        final_data['metadata'] = {
            'source_file': os.path.abspath(pdf_path),
            'processing_date': datetime.now().isoformat(),
            'converter_version': '2.0',
            'file_size_bytes': os.path.getsize(pdf_path)
        }
        
        # Write to JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, indent=2, ensure_ascii=False, sort_keys=False)
        
        print(f"JSON file created: {output_path}")
        self.print_conversion_summary(final_data)
        
        return output_path
    
    def print_conversion_summary(self, data: Dict[str, Any]):
        """Print a detailed summary of the conversion results."""
        print("\n" + "="*60)
        print("CONVERSION SUMMARY")
        print("="*60)
        
        print(f"Extraction method: {data.get('extraction_method', 'unknown')}")
        
        if 'total_pages' in data:
            print(f"Total pages: {data['total_pages']}")
        
        if 'math_analysis' in data:
            math_info = data['math_analysis']
            print(f"Mathematical expressions: {math_info['total_expressions']}")
            print(f"High confidence expressions: {math_info['high_confidence_count']}")
            print(f"Average confidence: {math_info['average_confidence']:.2f}")
            
            print("\nExpression categories:")
            for category, count in math_info['categories'].items():
                print(f"  - {category}: {count}")
        
        if 'full_text' in data:
            word_count = len(data['full_text'].split())
            print(f"Total words extracted: {word_count}")
        
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='Convert PDF with equations to JSON format')
    parser.add_argument('pdf_path', help='Path to the input PDF file')
    parser.add_argument('-o', '--output', help='Output JSON filename (optional)')
    parser.add_argument('--temp-dir', default='temp_dir', help='Temporary directory')
    parser.add_argument('--output-dir', default='output_dir', help='Output directory')
    
    args = parser.parse_args()
    
    try:
        converter = PDFToJSONConverter(temp_dir=args.temp_dir, output_dir=args.output_dir)
        output_path = converter.convert_pdf_to_json(args.pdf_path, args.output)
        
        print(f"\nConversion completed successfully!")
        print(f"Output file: {output_path}")
        
    except Exception as e:
        print(f"Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # If run without arguments, use test file
    if len(sys.argv) == 1:
        test_pdf = r"d:\workspace\french_cv\mcf_analytical_presentation_original.pdf"
        if os.path.exists(test_pdf):
            print("Running with test PDF...")
            converter = PDFToJSONConverter()
            converter.convert_pdf_to_json(test_pdf)
        else:
            print("No test PDF found. Usage: python pdf_to_json_universal.py <pdf_path>")
    else:
        main()
