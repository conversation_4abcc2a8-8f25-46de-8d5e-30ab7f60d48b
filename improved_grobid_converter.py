import json
import re
import os
from typing import Dict, List, Any, Optional

def process_unicode_text(text: str) -> str:
    """
    Convert unicode characters to LaTeX equivalents
    """
    if not text:
        return ""
    
    # Dictionary for common unicode to LaTeX conversions
    unicode_to_latex_map = {
        # Greek letters (lowercase)
        '\u03b1': r'\alpha',
        '\u03b2': r'\beta', 
        '\u03b3': r'\gamma',
        '\u03b4': r'\delta',
        '\u03b5': r'\varepsilon',
        '\u03b6': r'\zeta',
        '\u03b7': r'\eta',
        '\u03b8': r'\theta',
        '\u03b9': r'\iota',
        '\u03ba': r'\kappa',
        '\u03bb': r'\lambda',
        '\u03bc': r'\mu',
        '\u03bd': r'\nu',
        '\u03be': r'\xi',
        '\u03bf': r'o',
        '\u03c0': r'\pi',
        '\u03c1': r'\rho',
        '\u03c3': r'\sigma',
        '\u03c4': r'\tau',
        '\u03c5': r'\upsilon',
        '\u03c6': r'\phi',
        '\u03c7': r'\chi',
        '\u03c8': r'\psi',
        '\u03c9': r'\omega',
        
        # Greek letters (uppercase)
        '\u0393': r'\Gamma',
        '\u0394': r'\Delta',
        '\u0398': r'\Theta',
        '\u039b': r'\Lambda',
        '\u039e': r'\Xi',
        '\u03a0': r'\Pi',
        '\u03a3': r'\Sigma',
        '\u03a5': r'\Upsilon',
        '\u03a6': r'\Phi',
        '\u03a7': r'X',
        '\u03a8': r'\Psi',
        '\u03a9': r'\Omega',
        
        # Mathematical symbols
        '\u2212': r'-',           # minus sign (different from hyphen)
        '\u2207': r'\nabla',      # nabla
        '\u221e': r'\infty',      # infinity
        '\u221d': r'\propto',     # proportional to
        '\u2208': r'\in',         # element of
        '\u2211': r'\sum',        # summation
        '\u222b': r'\int',        # integral
        '\u2265': r'\geq',        # greater than or equal
        '\u2264': r'\leq',        # less than or equal
        '\u2260': r'\neq',        # not equal
        '\u2261': r'\equiv',      # equivalent
        '\u2248': r'\approx',     # approximately
        '\u00b1': r'\pm',         # plus minus
        '\u00d7': r'\times',      # multiplication
        '\u221a': r'\sqrt',       # square root
        '\u2202': r'\partial',    # partial derivative
        '\u2192': r'\rightarrow', # right arrow
        '\u2190': r'\leftarrow',  # left arrow
        '\u2200': r'\forall',     # for all
        '\u2203': r'\exists',     # there exists
        '\u2205': r'\emptyset',   # empty set
        '\u222a': r'\cup',        # union
        '\u2229': r'\cap',        # intersection
        '\u2286': r'\subseteq',   # subset or equal
        '\u22c5': r'\cdot',       # center dot
        '\u00b7': r'\cdot',       # middle dot
        '\u2026': r'\ldots',      # ellipsis
        '\u2032': r"'",           # prime
        '\u2033': r"''",          # double prime
        
        # Special characters
        '\u00a0': r'~',           # non-breaking space
        '\u2013': r'--',          # en dash
        '\u2014': r'---',         # em dash
    }
    
    # Apply unicode conversions
    for unicode_char, latex_equiv in unicode_to_latex_map.items():
        text = text.replace(unicode_char, latex_equiv)
    
    return text

def format_equation(raw_str: str, eq_num: Optional[str] = None) -> str:
    """
    Format equation string for LaTeX with proper mathematical notation
    Specifically handles the GROBID equation format
    """
    if not raw_str.strip():
        return ""
    
    # Process unicode characters first
    equation = process_unicode_text(raw_str)
    
    # Remove trailing period if present
    equation = equation.rstrip('.')
    
    # Handle specific patterns for better formatting
    
    # 1. Handle subscripts: "q t" -> "q_t", "p t" -> "p_t", etc.
    equation = re.sub(r'\b([a-zA-Z])\s+([a-zA-Z0-9]+)\b', r'\1_{\2}', equation)
    
    # 2. Handle superscripts for inverse: "M −1" -> "M^{-1}", "β −1" -> "\beta^{-1}"
    equation = re.sub(r'([a-zA-Z\\][a-zA-Z]*)\s*−\s*1\b', r'\1^{-1}', equation)
    equation = re.sub(r'([a-zA-Z\\][a-zA-Z]*)\s*-\s*1\b', r'\1^{-1}', equation)
    
    # 3. Fix specific issues with gamma and other Greek letters
    equation = re.sub(r'\\gamma([A-Z])', r'\\gamma \\, \1', equation)  # Add space after gamma before capital letters
    
    # 4. Handle function notation: "V (q)" -> "V(q)"
    equation = re.sub(r'([a-zA-Z])\s*\(\s*([^)]+)\s*\)', r'\1(\2)', equation)
    
    # 5. Handle differential notation: "d W t" -> "dW_t"
    equation = re.sub(r'd\s+([a-zA-Z])\s+([a-zA-Z0-9]+)', r'd\1_{\2}', equation)
    
    # 6. Handle gradient notation: "\nabla q" -> "\nabla_q"
    equation = re.sub(r'\\nabla\s+([a-zA-Z])', r'\\nabla_{\1}', equation)
    
    # 7. Clean up spacing around operators
    equation = re.sub(r'\s*=\s*', ' = ', equation)
    equation = re.sub(r'\s*\+\s*', ' + ', equation)
    equation = re.sub(r'(?<!\\gamma)\s*-\s*', ' - ', equation)  # Don't affect \gamma
    
    # 8. Add proper spacing around commas
    equation = re.sub(r',\s*', r', \\quad ', equation)
    
    # 9. Handle dt and dW with proper spacing
    equation = re.sub(r'\s+dt\b', r' \\, dt', equation)
    equation = re.sub(r'\s+d([WX])', r' \\, d\1', equation)
    
    # 10. Handle multiplication by constants like "2γβ" -> "2\gamma\beta"
    equation = re.sub(r'(\d+)\\gamma\\beta', r'\1\\gamma\\beta', equation)
    
    # 11. Clean up extra spaces
    equation = re.sub(r'\s+', ' ', equation).strip()
    
    # Format as LaTeX equation
    if eq_num and eq_num.strip():
        # Remove parentheses from equation number if present
        eq_num = eq_num.strip('()')
        return f"\\[\n{equation}\n\\tag{{{eq_num}}}\n\\]"
    else:
        return f"\\[\n{equation}\n\\]"

def process_text_block(text: str) -> str:
    """
    Process regular text blocks, handling unicode and basic LaTeX formatting
    """
    if not text or text.strip() == "EQUATION":
        return ""
    
    # Process unicode characters
    text = process_unicode_text(text)
    
    # Handle citations in brackets like [CV20], [PStoV21], etc.
    text = re.sub(r'\[([A-Z][A-Za-z0-9]+)\]', r'\\cite{\1}', text)
    
    # Handle mathematical expressions in text
    # Convert simple expressions like "q t ∈ R d" to "q_t \in \mathbb{R}^d"
    text = re.sub(r'\b([a-zA-Z])\s+([a-zA-Z0-9]+)\s*\\in\s*R\s*([a-zA-Z0-9]+)', r'$\1_{\2} \\in \\mathbb{R}^{\3}$', text)
    text = re.sub(r'\b([a-zA-Z])\s+([a-zA-Z0-9]+)\s*\\in\s*R\b', r'$\1_{\2} \\in \\mathbb{R}$', text)
    
    # Handle other mathematical expressions in text
    text = re.sub(r'\\beta\s*\(\s*([^)]+)\s*\)', r'$\\beta(\1)$', text)
    text = re.sub(r'\\gamma', r'$\\gamma$', text)
    text = re.sub(r'\\beta', r'$\\beta$', text)
    text = re.sub(r'\\mu', r'$\\mu$', text)
    text = re.sub(r'\\rho', r'$\\rho$', text)
    
    return text

def convert_grobid_json_to_latex(json_path: str, output_path: str = None) -> str:
    """
    Convert GROBID JSON to well-formatted LaTeX
    """
    
    # Generate output path if not provided
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(json_path))[0]
        output_path = f"{base_name}_converted.tex"
    
    # Load JSON data
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        raise ValueError(f"Error loading JSON file: {e}")
    
    # Start building LaTeX document
    latex_lines = [
        r"\documentclass{article}",
        r"\usepackage{amsmath,amssymb,mathtools}",
        r"\usepackage[utf8]{inputenc}",
        r"\usepackage{amsfonts}",
        r"\usepackage{geometry}",
        r"\usepackage{natbib}",
        r"\geometry{margin=1in}",
        r"\begin{document}",
        ""
    ]
    
    # Add title if available
    if 'title' in data and data['title']:
        title = process_unicode_text(data['title'])
        latex_lines.extend([
            f"\\title{{{title}}}",
            "\\maketitle",
            ""
        ])
    
    # Add abstract if available
    if 'pdf_parse' in data and 'abstract' in data['pdf_parse']:
        for abstract_block in data['pdf_parse']['abstract']:
            if abstract_block.get('text'):
                latex_lines.extend([
                    "\\begin{abstract}",
                    process_text_block(abstract_block['text']),
                    "\\end{abstract}",
                    ""
                ])
    
    # Process body text from pdf_parse
    if 'pdf_parse' in data and 'body_text' in data['pdf_parse']:
        current_section = None
        equation_count = 0
        
        for block in data['pdf_parse']['body_text']:
            # Handle section changes
            if block.get('section') and block['section'] != current_section:
                current_section = block['section']
                latex_lines.extend([
                    f"\\section{{{process_text_block(current_section)}}}",
                    ""
                ])
            
            # Handle equations
            if 'eq_spans' in block and block['eq_spans']:
                for eq in block['eq_spans']:
                    raw_str = eq.get('raw_str', '')
                    eq_num = eq.get('eq_num', '')
                    if raw_str.strip():
                        formatted_eq = format_equation(raw_str, eq_num)
                        latex_lines.extend([
                            formatted_eq,
                            ""
                        ])
                        equation_count += 1
            
            # Handle regular text
            elif 'text' in block and block['text'] and block['text'].strip() != "EQUATION":
                processed_text = process_text_block(block['text'])
                if processed_text.strip():
                    latex_lines.extend([
                        f"\\noindent {processed_text}",
                        ""
                    ])
    
    latex_lines.append(r"\end{document}")
    
    # Write to file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(latex_lines))
    
    print(f"✅ Conversion completed: {output_path}")
    print(f"📊 Found {equation_count} equations")
    return output_path

# Batch conversion function
def batch_convert_json_files(input_directory: str, output_directory: str = None) -> List[str]:
    """
    Convert all GROBID JSON files in a directory
    """
    import glob
    
    if output_directory is None:
        output_directory = input_directory
    
    os.makedirs(output_directory, exist_ok=True)
    
    # Find JSON files
    json_files = glob.glob(os.path.join(input_directory, "*.json"))
    
    converted_files = []
    
    for json_file in json_files:
        try:
            base_name = os.path.splitext(os.path.basename(json_file))[0]
            output_file = os.path.join(output_directory, f"{base_name}_converted.tex")
            
            convert_grobid_json_to_latex(json_file, output_file)
            converted_files.append(output_file)
            
        except Exception as e:
            print(f"❌ Error converting {json_file}: {e}")
    
    print(f"\n🎉 Batch conversion completed: {len(converted_files)} files converted")
    return converted_files

# Test and example usage
if __name__ == "__main__":
    # Test the equation formatting function with the example
    test_equation = "dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t ."
    formatted = format_equation(test_equation, "(4)")
    print("🧪 Test equation formatting:")
    print(formatted)
    print("\n" + "="*50 + "\n")
    
    # Convert the actual file
    json_file = r"output_dir\mcf_analytical_presentation.json"
    if os.path.exists(json_file):
        try:
            output_file = convert_grobid_json_to_latex(json_file, "mcf_analytical_presentation_improved.tex")
            print(f"📄 Generated: {output_file}")
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        print(f"❌ File not found: {json_file}")
    
    # Example batch conversion
    print("\n🔄 Starting batch conversion of all JSON files...")
    if os.path.exists("output_dir"):
        batch_convert_json_files("output_dir", "latex_converted")
