#!/usr/bin/env python3
"""
Example usage script for the PDF to JSON converter.
This script demonstrates how to use the converter with different types of PDFs.
"""

import os
import sys
from pdf_to_json_enhanced import PDFToJ<PERSON>NConverter

def test_converter():
    """Test the converter with available PDF files."""
    
    # Initialize converter
    converter = PDFToJSONConverter()
    
    # List of test PDFs in the workspace
    test_pdfs = [
        "mcf_analytical_presentation_original.pdf",
        "mcf_analytical_presentation.pdf"
    ]
    
    successful_conversions = []
    failed_conversions = []
    
    for pdf_name in test_pdfs:
        pdf_path = os.path.join(os.getcwd(), pdf_name)
        
        if os.path.exists(pdf_path):
            print(f"\n{'='*60}")
            print(f"Testing with: {pdf_name}")
            print(f"{'='*60}")
            
            try:
                output_path = converter.convert_pdf_to_json(pdf_path)
                successful_conversions.append((pdf_name, output_path))
                
                # Load and display sample content
                import json
                with open(output_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"\nSample content from {pdf_name}:")
                if 'full_text' in data and data['full_text']:
                    sample_text = data['full_text'][:500] + "..." if len(data['full_text']) > 500 else data['full_text']
                    print(f"Text sample: {sample_text}")
                
                if 'math_analysis' in data:
                    math_count = data['math_analysis']['total_expressions']
                    print(f"Mathematical expressions found: {math_count}")
                    
                    if math_count > 0:
                        print("Sample math expressions:")
                        for i, expr in enumerate(data['math_analysis']['category_details'].get('display_latex', [])[:3]):
                            print(f"  {i+1}. {expr['content'][:100]}...")
                
            except Exception as e:
                print(f"Failed to convert {pdf_name}: {str(e)}")
                failed_conversions.append((pdf_name, str(e)))
        else:
            print(f"PDF not found: {pdf_path}")
            failed_conversions.append((pdf_name, "File not found"))
    
    # Summary
    print(f"\n{'='*60}")
    print("CONVERSION SUMMARY")
    print(f"{'='*60}")
    print(f"Successful conversions: {len(successful_conversions)}")
    for pdf_name, output_path in successful_conversions:
        print(f"  ✓ {pdf_name} → {os.path.basename(output_path)}")
    
    print(f"\nFailed conversions: {len(failed_conversions)}")
    for pdf_name, error in failed_conversions:
        print(f"  ✗ {pdf_name}: {error}")

def demonstrate_features():
    """Demonstrate specific features of the converter."""
    
    print("PDF to JSON Converter - Feature Demonstration")
    print("="*60)
    
    # Check if we have any PDF files
    current_dir = os.getcwd()
    pdf_files = [f for f in os.listdir(current_dir) if f.endswith('.pdf')]
    
    if not pdf_files:
        print("No PDF files found in current directory.")
        print("Please place a PDF file in the workspace to test the converter.")
        return
    
    print(f"Found {len(pdf_files)} PDF file(s):")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"  {i}. {pdf_file}")
    
    # Use the first PDF for demonstration
    test_pdf = pdf_files[0]
    print(f"\nUsing {test_pdf} for demonstration...")
    
    converter = PDFToJSONConverter()
    
    try:
        output_path = converter.convert_pdf_to_json(test_pdf)
        
        # Analyze the output
        import json
        with open(output_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\nDetailed Analysis of {test_pdf}:")
        print("-" * 40)
        
        # Basic info
        print(f"Extraction method: {data.get('extraction_method', 'Unknown')}")
        print(f"File size: {data.get('metadata', {}).get('file_size_bytes', 0)} bytes")
        
        # Text analysis
        if 'full_text' in data:
            text = data['full_text']
            words = text.split()
            print(f"Total characters: {len(text)}")
            print(f"Total words: {len(words)}")
            print(f"Average word length: {sum(len(word) for word in words) / len(words):.1f}")
        
        # Math analysis
        if 'math_analysis' in data:
            math_data = data['math_analysis']
            print(f"\nMathematical Content Analysis:")
            print(f"Total expressions: {math_data['total_expressions']}")
            print(f"High confidence: {math_data['high_confidence_count']}")
            print(f"Average confidence: {math_data['average_confidence']:.2f}")
            
            print(f"\nExpression types:")
            for expr_type, count in math_data['categories'].items():
                print(f"  {expr_type}: {count}")
            
            # Show some examples
            if math_data['total_expressions'] > 0:
                print(f"\nSample expressions:")
                all_expressions = []
                for category, expressions in math_data['category_details'].items():
                    all_expressions.extend(expressions)
                
                # Sort by confidence and show top 5
                sorted_expressions = sorted(all_expressions, 
                                          key=lambda x: x.get('confidence', 0), 
                                          reverse=True)[:5]
                
                for i, expr in enumerate(sorted_expressions, 1):
                    print(f"  {i}. [{expr['type']}] {expr['content'][:80]}...")
                    print(f"     Confidence: {expr.get('confidence', 0):.2f}")
        
        print(f"\nOutput saved to: {output_path}")
        
    except Exception as e:
        print(f"Error during demonstration: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            test_converter()
        elif sys.argv[1] == "--demo":
            demonstrate_features()
        else:
            print("Usage:")
            print("  python example_usage.py --test   # Test with multiple PDFs")
            print("  python example_usage.py --demo   # Detailed demonstration")
    else:
        demonstrate_features()
