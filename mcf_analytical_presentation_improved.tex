\documentclass{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}
\usepackage{mathtools}
\usepackage[margin=1in]{geometry}
\usepackage{hyperref}

\begin{document}

\title{Ãcoles_{d}'Ã©tÃ© 06/2015 New Perspectives in Markov Chain}

\author{Urbain Vaes, Monte Carlo}

\maketitle

\begin{abstract}
La structure de ce document est la suivante: les premiÃ¨res pages contiennent un bref curriculum vitae et une liste de mes contributions. En section_{2}, mon activitÃ© de recherche antÃ©rieure est rÃ©sumÃ©e briÃ¨vement et structurÃ©e selon trois catÃ©gories principales. Mes contributions majeures relatives Ã chacune de ces catÃ©gories sont ensuite dÃ©taillÃ©es en section_{3}. Finalement, en section_{4}, mon programme de recherche Ã court et moyen termes est prÃ©sentÃ©.
\end{abstract}

\section*{Postdoctoral experience}

-L'objectif principal du postdoctorat est le dÃ©veloppement et_{l}'analyse de mÃ©thodes de rÃ©duction de variance pour le calcul de coefficients de transport en physique statistique computationelle.

-Enseignement : J'ai encadrÃ© un groupe de trois Ã©tudiants dans le cadre du projet de premiÃ¨re annÃ©e Ã l'Ãcole des Ponts (28h). Le but du projet que_{j}'ai supervisÃ© Ã©tait_{d}'implÃ©menter et_{d}'analyser une mÃ©thode spectrale pour la rÃ©solution_{d}'Ã©quations de Fokker-Planck.

ExpÃ©rience postoctorale antÃ©rieure -Groupe de recherche_{d}'Analyse appliquÃ©e et numÃ©rique (Applied and Numerical Analysis).

-Enseignement : j'Ã©tais chargÃ© du cours de Master intitulÃ© "MÃ©thodes numÃ©riques pour les processus stochastiques" (Computational Stochastic Processes) pendant le trimestre de printemps 2020. Mes responsabilitÃ©s incluaient la prÃ©paration et_{l}'enseignement des cours (30h de cours), la prÃ©paration et correction de sÃ©ances_{d}'exercices et_{d}'un projet, et la prÃ©paration et correction de_{l}'examen.

-Centres_{d}'intÃ©rÃªt scientifiques : MÃ©thodes numÃ©riques pour les Ã©quations de Fokker-Planck-Kolmogorov ; MÃ©thodes stochastiques en dynamique molÃ©culaire ; Limites de champ moyen pour des systÃ¨mes de particules en interaction ; MÃ©thodes sans dÃ©rivÃ©es pour les problÃ¨mes inverses. -Titre de la thÃ¨se : Sujets en modÃ©lisation multi-Ã©chelle : analyse numÃ©rique et applications (Titre original : Topics in multiscale modeling : numerical analysis and applications).

-Enseignement : J'ai Ã©tÃ© assistant (en anglais, graduate teaching assistant) pour les cours suivant : Analyse rÃ©elle (20h), ProbabilitÃ©s et statistiques (20h), Ãquations diffÃ©rentielles (20h) et Introduction Ã l'analyse numÃ©rique (20h).

\section{-2014}

Master de Recherche en Sciences MathÃ©matiques, Imperial College London, Royaume-Uni -SpÃ©cialisation : Calcul scientifique.

-ThÃ¨se : Simulation_{d}'Ã©quations stochastiques Ã plusieurs Ã©chelles (Titre original : Simulation of stochastic equations with multiple scales), supervisÃ©e par Prof. G.A. Pavliotis.

-Mention : distinction (Cours : 91/100, Recherche : 96/100).

\section{-2013}

MSc ingÃ©nieur civil mÃ©canicien, UniversitÃ© catholique de Louvain, Belgium -SpÃ©cialisation : MÃ©thodes numÃ©riques pour la mÃ©canique des solides et des fluides.

-ThÃ¨se : Ãlements finis discontinus pour les EDP elliptiques de_{4} e ordre, supervisÃ©e by Prof. J.-F. Remacle.

-Mention : La plus grande distinction avec les fÃ©licitations du jury. Publications scientifiques

\section{Contributions logicielles}

Au cours de ma recherche, j'ai participÃ© au dÃ©veloppement des codes mathÃ©matiques suivants, qui ont Ã©tÃ© Ã©crits dans le but soit de valider des rÃ©sultats thÃ©oriques, soit_{d}'illustrer des mÃ©thodes numÃ©riques, soit_{d}'analyser des systÃ¨mes physiques ou mathÃ©matiques.

â¢ Hermipy: https://github.com/urbainvaes/hermipy.

Hermipy est une bibliothÃ¨que Python qui permet_{d}'automatiser la plupart des opÃ©rations impliquÃ©es dans_{l}'implÃ©mentation de mÃ©thodes spectrales de type Fourier/Hermite. La bibliothÃ¨que utilise les structures de donnÃ©es fournies par la bibliothÃ¨que NumPy, et elle dÃ©pend aussi de SymPy pour les calculs symboliques. Les calculs intensifs sont pris en charge par un composant compilÃ© en_{C}++, avec lequel la bibliothÃ¨que Python communique grÃ¢ce Ã Boost. La bibliothÃ¨que Hermipy_{a} Ã©tÃ© dÃ©veloppÃ©e et utilisÃ©e pour produire des rÃ©sultats numÃ©riques dans trois de mes publications. En plus de ces contributions, j'ai dÃ©veloppÃ© pendant mon temps libre des logiciels utilitaires non mathÃ©matiques que beaucoup de programmeurs utilisent quotidiennement, et qui sont accessibles sur mon profil Github. Toutes mes contributions logicielles sont accessibles sous licences libres.

\section{Brief summary of my past research activity}

Most of my past activity is at the interface between stochastic differential equations (SDEs), partial differential equations (PDEs), Bayesian statistics and numerical methods. Many of the problems_{I} am interested in are stochastic in nature, but_{I} often approach them by means of PDE methods, for example via Kolmogorov backward and forward equations. My main research contributions can be divided in three main categories:

Analysis and simulation of systems with multiple scales. Multiscale modeling was one of the main themes of my PhD research. I studied, in separate works, systems with multiple time scales and problems with multiple length scales.

In_{a} collaboration with Grigorios Pavliotis and Assyr Abdulle, I developed and analyzed an efficient method for solving numerically systems of SDEs with multiple time scales. Multiscale stochastic systems arise frequently in applications, for example in materials science and climate modeling. When the scale separation is clear, the evolution of the slow variables is generally accurately described by_{a} closed effective equation which, depending on the problem, can be obtained by averaging or homogenization of the multiscale system. Our aim was to propose_{a} deterministic spectral method for efficiently calculating, in the homogenization setting, the drift and diffusion coefficients appearing in this equation.

Later in my PhD, I had the opportunity to conduct research in the Department of Chemical Engineering under the supervision of Serafim Kalliadasis, where_{I} developed, in collaboration with Serafim and other coworkers, a novel finite element method for the Cahn-Hilliard equation with wetting. The Cahn-Hilliard equation is_{a} fourth order parabolic equation that is widely used for modeling immiscible binary mixtures via_{a} phase field approach. It contains_{a} small parameter encoding the typical interface width which, in general, is much smaller than the characteristic length of the domain. Our main contribution was to extend_{a} class of linear, semi-implicit finite element schemes from the case of_{a} homogeneous boundary condition to that of_{a} cubic boundary condition for the phase field. We also proposed_{a} mesh adaptation strategy that enables to accurately capture the interfacial regions, and we implemented the methods in Freefem++.

Study of dynamical systems with colored noise or memory. In my last year of PhD, together with Grigorios Pavliotis and Susana Gomes, I studied mean-field limits for systems of weakly interacting diffusion processes. Systems of this type appear in_{a} wide variety of applications, ranging from molecular dynamics to the social sciences and machine learning, and they are known to exhibit rich dynamics. Our original contribution was to consider noise processes that are not delta-correlated in time. Noise models of this type, known as colored noise models, enable_{a} more realistic representation of physical systems but their analysis presents additional challenges compared to the case of white noise. Our main aim was to scrutinize the effect of the correlation time of noise on the number and stability of the steady states of the nonlocal equation of Fokker-Planck type that arises in the mean-field limit, in order to better understand the phase transitions exhibited by the particle system.

Later, in collaboration with Gabriel Stoltz and Grigorios Pavliotis, I studied scaling limits for the rate of convergence to equilibrium and the effective diffusion coefficient of generalized Langevin dynamics. These dynamics were initially proposed as_{a} model for describing the motion of_{a} particle interacting with_{a} heat bath at equilibrium, and they now have applications in many other areas of science and engineering, ranging from polymer dynamics to sampling and global optimization. They can be viewed as Langevin dynamics driven by colored noise and they can be recast, in the quasi-Markovian setting, as_{a} system of SDEs on an extended phase space. Our main objective was to obtain more quantitative bounds on the rate of convergence to equilibrium than previously available, which provided the means for deriving new asymptotic results for the effective diffusion coefficient associated with the dynamics in the periodic setting.

As_{a} postdoctoral researcher in the MATHERIALS team since November 2020, I collaborate with Gabriel Stoltz on the study of variance reduction approaches for the calculation of transport coefficients in molecular dynamics. Our first project concerns the development of_{a} variance reduction method based on control variates for calculating the mobility of two-dimensional Langevin dynamics in_{a} periodic potential. The aim is to shed light on the behavior of the mobility in the underdamped limit which, except in dimension_{1}, is not currently well understood by the scientific community.

Derivative-free methods for Bayesian inference. When_{I} was_{a} postdoctoral researcher at Imperial College London, my research focused in large part on the analysis and development of gradient-free methods for solving inverse problems, specifically for the tasks of minimizing_{a} regularized misfit or sampling from_{a} Bayesian posterior distribution. In many applications of inverse problems, the forward model is expensive to evaluate and its derivatives are not available or too costly to obtain, so gradient-free methods that require few evaluations of the forward model are an attractive proposition. In recent years, two important developments opened the door to promising research avenues in this area. Firstly, the ensemble Kalman filter was extended from state estimation problems to inverse problems, initially for misfit minimization and later for posterior sampling. The resulting gradient-free methods are proving empirically very successful, partly because they are based on interacting particle systems and can therefore leverage recent advances in parallel computing, but they still lack_{a} robust theoretical footing. Secondly, a trend of studying numerical algorithms based on interacting replicas from_{a} mean-field viewpoint emerged from the optimization community and brought considerable insight, particularly into the behaviour of consensus-based optimization methods. The meanfield viewpoint holds considerable promise for improving our understanding of interacting particle algorithms.

My first contribution in this area was the derivation, in collaboration with JosÃ© Carrillo, of_{a} sharp contraction estimate for_{a} mean-field Fokker-Planck equation associated with an ensemble Kalman-based method for posterior sampling. I then collaborated with Grigorios Pavliotis and Andrew Stuart on the development of_{a} sampling method based on_{a} multiscale dynamics that can be refined systematically to return the true solution, thereby overcoming the issue of uncontrolled gradient approximations affecting ensemble Kalman-based methods. More recently, in ongoing work with JosÃ© Carrillo, Franca Hoffmann and Andrew Stuart that is almost completed, we are investigating whether ideas from consensus-based methods, which are proving to be interesting and viable for optimization, can be useful also for the purpose of sampling. As_{a} first step in this direction, we are scrutinizing the convergence properties of the nonlocal Fokker-Planck equation associated with the method.

3 Summary of main contributions

\section{Spectral methods for multiscale stochastic differential equations}

Description of the contribution. This work focused on the development and analysis, through rigorous proofs and numerical experiments, of_{a} spectral method based on Hermite polynomials for multiscale stochastic equations. More precisely, we developed_{a} numerical method for solving through_{a} homogenization approach multiscale stochastic differential equations (SDEs) of the type

dX Îµ t = 1 Îµ f (X Îµ t , Y Îµ t ) \, dt + â 2 Ï x dW_{x} (t), dY Îµ t = 1 Îµ 2 h(X Îµ t , Y Îµ t ) \, dt + â 2 Îµ Ï y dW_{y} (t).

Here

X Îµ t â R_{m} , Y Îµ t â R_{n} ,

and Îµ is_{a} small parameter encoding the timescale separation between the processes_{X} Îµ and_{Y} Îµ . The parameters Ï x â R mÃm , Ï y â R nÃn are constant matrices, and_{W} x , W_{y} are independent_{m} and_{n}-dimensional Brownian motions, respectively. In order for this system to have_{a} welldefined limit as Îµ â 0, it is assumed for all_{x} â R_{m} that the equation for_{Y} Îµ t admits_{a} unique invariant measure Ï x â when_{X} Îµ t = x is taken as_{a} fixed parameter, and that the slow drift_{f} (x, â¢ ) has mean_{0} with respect Ï x â . When Îµ âª 1, traditional methods for the numerical solution of SDEs are not suitable for this problem, because the time step required to perform_{a} stable and accurate time integration would be prohibitively small. It is well-known, however, that the slow process_{X} Îµ converges weakly in_{C}([0, T ]), in the as limit Îµ â 0, to the solution of_{a} simpler effective equation,

\begin{equation}
dX_{t} = F (X_{t}) \, dt + A(X_{t}) \, dW (t),
\label{eq:1}
\end{equation}

where_{W} is_{a} standard Brownian motion on_{R} m , in general different from_{W} x and_{W} y . The effective drift and diffusion coefficients, F and_{A}, can be expressed in terms of the solution to_{a} Poisson partial differential equation:

\begin{equation}
âL_{0} (x) Ï(x, y) = f (x, y).
\label{eq:2}
\end{equation}

Here the operator_{L} 0 (x) is, up to the factor Îµ â2 , the generator of the fast dynamics when_{X} Îµ t = x is viewed as_{a} fixed parameter. The Poisson equation (2) should be viewed as_{a} partial differential equation (PDE) on the state-space_{R} n of_{Y} Îµ t , with_{x} only playing the role of_{a} fixed parameter. When Îµ is sufficiently small, the slow dynamics_{X} Îµ t can be well approximated by solving the effective equation (1) instead of the full multiscale system. To this end, we proposed to use_{a} spectral method based on Hermite polynomials in order to calculate an approximate solution to the Poisson equation (2) , which can then be employed in order to calculate the effective coefficients_{F} and_{A} of the homogenized equation. We proved error estimates showing the super-algebraic convergence of the spectral method under appropriate smoothness and decay assumptions, and we provided numerical evidence for the efficiency of the method when the dimension_{n} of the state-space of_{Y} Îµ t is sufficiently small. A comparison with existing methods and an application to singularly perturbed stochastic PDEs were also presented.

Collaborators. This work was_{a} collaboration with Grigorios Pavliotis (Maths, Imperial College) and Assyr Abdulle (Maths, EPFL).

Originality and impact. The originality of our work lies in the fact that, in contrast with averagingbased methods, such as the heterogeneous multiscale method or the equation-free method, which rely on Monte Carlo simulations, our method is based on_{a} deterministic spectral method, providing better efficiency in small dimensions.

Dissemination. This work led to the publication [APV17] and to the development of_{a} Python/C++ library for Hermite and Fourier/Hermite spectral methods, called Hermipy. The associated code is freely available on Github.

\section{Study of dynamical systems with colored noise or memory}

Description of the contribution. Time-correlated noise, also known as colored noise, is often employed as_{a} realistic alternative to white noise in models of physical systems. Recently, dynamical systems with colored noise, and more generally those with inertia or memory, have proven useful also in the context of sampling and optimization, including for applications in the big-data and machine-learning settings. Two of my research works aimed at improving our understanding of systems of this type.

â¢ Together with Susana Gomes and Grigorios Pavliotis, we studied systems of particles driven by colored noise and interacting via_{a} quadratic Curie-Weiss potential. In the case of white noise, particle systems of this type are known to exhibit rich dynamics, and in particular one or several phase transitions, when the external potential_{V} has several local minima. Our main aim was to study the effect of colored noise on phase transitions in the case of the standard double-well potential. More precisely, we studied the effect of the correlation time of the noise on the bifurcation diagram for the steady states of the nonlocal Fokker-Planck equation that governs the evolution of the particle system in the mean-field limit, with the inverse temperature as bifurcation parameter. To this end, we first employed formal asymptotics with respect to the correlation time of the noise for the steady states. In order to obtain results that are valid beyond the small correlation time regime, we also used_{a} Hermite spectral method in order to solve the time-dependent mean-field Fokker-Planck equation in the longtime limit and thereby find the steady states.

â¢ With Gabriel Stoltz and Grigorios Pavliotis, we studied the generalized Langevin equation (GLE) in_{a} periodic potential. Generalized Langevin dynamics are often used in molecular dynamics and nonequilibrium statistical mechanics to model_{a} particle interacting with_{a} heat bath at equilibrium. For_{a} large class of GLEs, a functional central limit theorem holds: the rescaled position process Î½ q(t/Î½ 2 ) converges, weakly in_{C}([0, T ]; R) as Î½ â 0, to_{a} pure diffusion, with an effective diffusion coefficient that depends on the parameters of the memory kernel. In our work, we first proved sharp longtime convergence estimates to the stationary state for the GLE using techniques from_{H} 1 hypocoercivity and hypoelliptic regularization. Based on these estimates, we then proved asymptotic results for the effective diffusion coefficient in three asymptotic regimes of particular interest: short correlation, large friction and low friction. Finally, we employed_{a} recently developed spectral numerical method in order to calculate the effective diffusion coefficient for_{a} wide range of parameters the memory kernel, thereby corroborating our asymptotic results.

Collaborators. The first work described above was conducted in collaboration with Susana Gomes (Maths, Warwick) and Grigorios Pavliotis. The second was_{a} collaboration with Grigorios Pavliotis and Gabriel Stoltz (Maths, ENPC \& Inria).

Originality and impact. The use of colored noise in the interacting particle system studied in the first project presents several difficulties compared to the white noise case: the mean-field Fokker-Planck equation no longer has an explicit gradient structure, there is no longer an explicit expression for its steady states, and the equation is hypoelliptic. Using colored noise also leads to more computationally expensive simulations of the mean-field PDE, because of the extra dimensions corresponding to the noise.

The main difficulty encountered in the second project was to obtain, based on Villani's hypocoercivity framework, convergence rates with an explicit dependence on the physical parameters of the equation. Since off-the-shelf hypocoercivity results were not sufficiently quantitative to this end, we developed_{a} methodology for obtaining the scalings of the coefficients appearing in the modified inner product and, from there, deriving precise convergence rates. It would be worthwhile to investigate whether our method can be extended to other high-dimensional hypocoercive systems, such as atom chains. We also identified an interesting open question related to the GLE: is it possible to obtain_{L} 2 resolvent estimates more directly than via the_{H} 1 approach, for example through the direct_{L} 2 hypocoercivity framework developed in [5, 6] , or through the more recent approach based on Schur complements developed in [1] ? (See the end of section_{4} for the list of references.)

Dissemination. This work led to two publications, [GPV20] and [PStoV21] , and it also led to the further development of Hermipy.

\section{Derivative-free methods for inverse problems}

Description of the contribution. Inverse problems are ubiquitous because they formalize the integration of data with mathematical models. In general, they can be formulated in the following form: find an unknown parameter_{u} â X from data_{y} â Y , given that the two are related by an equation of the form

\begin{equation}
y = G(u) + Î·,
\label{eq:3}
\end{equation}

where_{G} is the parameter-to-data map and Î· is observational noise whose exact value is unknown but whose statistical properties may be known. In many scientific applications, the forward model is expensive to evaluate and adjoint computations are difficult to employ; in this setting, derivative-free methods which involve_{a} small number of forward model evaluations are_{a} very attractive proposition. During my postdoc at Imperial College London, one of my main research activities was the analysis and development of derivative free methods for Bayesian inverse problems. My contributions in this area are the following:

â¢ With JosÃ© Carrillo, we proved sharp convergence estimates in Wasserstein distance for inversion and sampling methods based on the ensemble Kalman filter. To this end we employed techniques from optimal transport in order to study the partial differential equations that describe the evolution of the interacting particle systems in the limit of infinitely many particles. Our estimates show that, at the mean-field level, the convergence rate of ensemble Kalman methods is constant across all linear inverse problems.

â¢ In later work with Grigorios Pavliotis and Andrew Stuart, we proposed_{a} novel refineable derivative-free method based on_{a} multiscale dynamics for posterior sampling or maximum_{a} posteriori estimation. Using techniques from multiscale and numerical analysis, we proved the convergence of the approximate solution, at both the continuous-time and discrete-time levels, to_{a} preconditioned overdamped Langevin dynamics in an appropriate limit for the parameters of the method. We also demonstrated the efficiency of the method through careful numerical experiments.

â¢ In_{a} more recent work that will soon be submitted for publication, we demonstrate how ideas from consensus-based optimization could be employed for the purpose of sampling. We propose an efficient sampling method based on consensus and prove its key properties, such as affine invariance and the exponential convergence of the associated mean-field Fokker-Planck equation in simple settings. We also show how, with an appropriate set of parameters, the method can be employed for optimization.

Collaborators. The first work described above was realized in collaboration with JosÃ© Carrillo (Maths, Oxford). The second work was carried out in collaboration with Andrew Stuart (Maths, Caltech) and Grigorios Pavliotis, and the third is the result of_{a} collaboration with JosÃ© Carrillo, Franca Hoffmann (Maths, University of Bonn) and Andrew Stuart.

Originality and impact. Ensemble Kalman based interacting particle systems have proven empirically successful in the context of Bayesian inverse problems, but few theoretical works have studied their convergence properties. In addition, they suffer from the fact that they cannot be systematically refined to sample more precisely from the posterior distribution. The aims of the first two contributions above were respectively to advance the state of understanding of ensemble Kalman methods, and to propose an alternative method that overcomes the issue of uncontrolled gradient approximations affecting the Ensemble Kalman sampler. The third contribution was more exploratory; it aimed to determine the viability of consensusbased approaches, which had previously been employed in the context of optimization, for the purpose of sampling. Through this work, and in particular through the third study above, we identified promising research avenues that we hope to pursue in the future. For example, we showed empirically that progressively decreasing_{a} temperature parameter, in the style simulated annealing, leads to_{a} much faster convergence of consensus-based optimization and sampling methods, and we believe that this behavior could be explained and studied via rigorous mathematical analysis.

Dissemination This work led to the paper [CV20], the submitted preprint [PStuV21] , and the study [CHSV21] which will soon be submitted for publication. It also led to the development of_{a} code implementing ensemble Kalman and consensus-based methods for sampling and inversion (Bayesicle), which is still under active development.

\section{Research program and insertion within the research laboratory}

I present in this section_{a} research program that_{I} would be very enthusiastic to incorporate, in part or in full, in my future research activity. I will also be very happy to develop, in parallel, new collaborations with members of the Laboratoire de MathÃ©matiques AppliquÃ©es de CompiÃ¨gne (L.M.A.C.) on subjects related to the areas of expertise of the laboratory. I discuss at the end of this section my insertion within_{L}.M.A.C. and the interactions_{I} envisage with members of the laboratory.

\section*{Research program title: Sampling in high dimensions: from mathematical analysis to practical algorithms}

The main aim of this research project is to improve, mathematically analyze and implement efficient sampling methods for inverse problems and computational statistical physics. The project lies at the interface between partial differential equations (PDEs), numerical analysis and computational statistics, and it comprises both theoretical and numerical aspects. On the one hand, I intend to study and improve sampling and optimization methods based on interacting particle systems, particularly in the context of statistical inverse problems. Work of this type will help lay the theoretical foundations of this emerging field and, when combined with changes in computer architectures that facilitate the efficient implementation of interacting particle systems, has the potential to significantly impact applications. On the other hand, I propose to develop efficient variance reduction approaches in computational statistical physics, with an emphasis on nonequilibrium systems and the ambition to accelerate sampling methods in this field, which will open the door to new applications. I very much believe in the cross-fertilization and blending of successful techniques from one subfield to the other. Below, I first present the context motivating this research program and then develop its two parts.

Context. Many scientific applications require the calculation of expectations with respect to high-dimensional probability distributions. A widely-used approach to this end, known as the Markov chain Monte Carlo (MCMC) method, is to simulate_{a} long trajectory of_{a} stochastic dynamics that admits the target distribution as unique invariant measure, and to approximate expectations by time averages over this trajectory. Although the original MCMC method dates back to the 1950s [16] , the development and analysis of sampling methods remains at present an extremely active area of mathematical research, driven by the ever-increasing amount of data available to scientists, a desire to understand high-dimensional problems, and changes in computer architecture. As mentioned in the introduction, we discuss in this research project two mathematical disciplines in which access to robust and efficient numerical methods for high-dimensional sampling is particularly important: computational statistical methods for inverse problems and computational statistical physics. These two application domains present different challenges and constraints.

â¢ The language of inverse problems provides_{a} mathematical formalization for the assimilation of data into predictive models. Approaching these problems through the Bayesian viewpoint brings considerable insight, as it enables to assign probabilities to all possible solutions and thereby to quantify uncertainty [22] . In this setting, the usual method for acquiring information on the true solution is to generate samples from the probability distribution that best describes it given the data -the Bayesian posterior. This distribution is typically high-dimensional and known up to_{a} constant factor, but in many applications it is expensive to evaluate and its derivatives are unavailable or too costly to obtain. Gradient-free sampling methods that require few evaluations of the target distribution are thus highly desirable. An additional difficulty is that the target distribution often exhibits strong anisotropy: the condition number of the Hessian of the target distribution is large in regions of high posterior probability density. In order to appropriately handle this difficulty, a frequent additional requirement for sampling methods in the context of Bayesian inverse problems is that they are invariant under affine changes of coordinates, a property known as affine invariance [11, 10] .

â¢ Molecular dynamics, also known as computational statistical physics, focuses on the simulation of atomistic systems using Newtonian models, with the primary aim of calculating macroscopic properties of matter. Standard examples of such properties include static (or thermodynamic) quantities such as heat capacities and equations of state relating pressure, density and temperature, as well as dynamic quantities such as transport coefficients (e.g. the mobility, the shear viscosity and the thermal conductivity). Molecular dynamics may also be employed in order to gain_{a} better understanding of atomistic systems whose direct observation is too expensive or beyond the current capabilities of experimental physics. In addition to providing valuable insight into the behavior of microscopic systems, these "in silico experiments" can be used for guiding technological developments, particularly in the field of materials science. Other areas where molecular dynamics plays an important role include biology, chemistry and drug discovery.

There are several specific difficulties associated with sampling in the context of molecular dynamics. First, the target distribution is often multimodal, leading to metastability issues of many sampling algorithms: most of the time, the iterates are trapped in some region of high probability density -a metastable state -and on rare occasions they hop to another metastable state. This issue often leads to large statistical errors of ergodic estimators as it requires to simulate very long trajectories in order to guarantee_{a} good exploration of the configuration space. A second difficulty is that the stochastic dynamics of interest are usually degenerate, with noise acting only on some degrees of freedom, and so dedicated mathematical techniques are required for their analysis. Finally, in nonequilibrium settings, there is generally no explicit expression for the target probability distribution and, consequently, many of the standard methods for variance reduction are not directly applicable.

1. Sampling and optimization methods for statistical inverse problems. In recent years, there has been significant activity to develop sampling methods based on interacting particle systems, which can leverage recent advances in parallel computing. Examples of current interest include interacting particle MCMC methodologies [4] , Stein variational gradient descent [14, 15] , the ensemble Kalman sampler [9] and consensus-based sampling [CHSV21] . Being derivative-free and affine invariant, the latter two methods are particularly well-suited for inverse problems. Since they are based on systems of interacting stochastic differential equations, they may be studied by means of the nonlocal Fokker-Planck PDEs associated with the systems in the limit of infinitely many particles. This approach of studying interacting particle numerical algorithms from_{a} mean-field viewpoint was first proposed in [19] in an optimization context and has since brought considerable insight. Since both ensemble Kalman and consensus-based sampling were proposed recently, there are many exciting research avenues concerning their analysis as well as possible improvements and extensions. The research_{I} propose to undertake will help to make significant advances in this emerging field.

â¢ Firstly, I propose to explore several research directions that naturally follow from previous work in [CV20] and [CHSV21] . I will perform_{a} quantitative analysis of the connection between the interacting particle systems at the roots of ensemble Kalman and consensus based methods and their mean field limit, which will provide insight into the optimal number of particles to use in such simulations. I will also endeavor to derive more general convergence results for the nonlocal Fokker-Planck equations describing these methods in the mean field limit than those previously obtained, which focused predominantly on the Gaussian setting. Next, I intend to study theoretically_{a} variation of consensus-based sampling and optimization where_{a} temperature parameter is progressively decreased, in the style of simulated annealing. This approach is observed in our numerical experiments to significantly accelerate convergence, but still lacks_{a} theoretical footing. On the numerical front, it will be worthwhile to examine whether consensus-based sampling and its optimization variant are competitive outside the context of Bayesian inverse problems, e.g. for high-dimensional optimization problems arising in the training of large neural networks.

â¢ Secondly, I propose to study whether the accuracy of ensemble Kalman and consensus-based sampling methods can be improved. Indeed, a drawback of these approaches is that they generate samples from_{a} probability distribution that only approximates the desired target distribution. Consensusbased sampling, for example, aims at producing samples from the multivariate normal distribution that best approximates, in an appropriate sense, the target density. This can lead to potentially large errors when the target density is very different from Gaussian, making the method ill-suited in these situations. In the context of Bayesian inference, the posterior distribution is generally close to Gaussian provided that sufficiently many data are available (by the Bernstein-von Mises theorem), so this limitation is somewhat mitigated, but large errors can still arise when data are scarce. In order to remedy this issue, a promising approach would be to investigate whether ensemble Kalman and consensus-based methods can be coupled to_{a} Metropolis-Hastings accept/reject step for better accuracy. Alternatively, one could integrate these methods in_{a} composite sampling methodology, such as the one based on parameter-to-data map emulation recently proposed in [2] . Finally, it would be very useful to develop methods for estimating_{a} posteriori the discrepancy between the actual target distribution and its numerical approximation.

2. Sampling methods in computational statistical physics. In recent decades, there has been significant activity devoted to improving and analyzing variance reduction methods for sampling algorithms, both for standard Monte Carlo methods and for MCMC methods. Let us mention, for example, recent developments in importance sampling [3, 13] and control variates [17, 18, 20] . In this project, I propose to develop, study and improve variance reduction approaches in molecular dynamics, particularly for the computation of transport coefficients. For concreteness of the discussion, let us consider the Langevin equation, which is_{a} paradigmatic model in molecular dynamics:

\begin{equation}
dq_{t} = M â1 p_{t} \, dt, dp_{t} = ââ q_{V} (q_{t}) \, dt â Î^{3}M â1 p_{t} \, dt + 2Î^{3}Î^{2} â1 dW_{t} .
\label{eq:4}
\end{equation}

Here_{q} t â R_{d} and_{p} t â R_{d} are the position and momentum of_{a} particle in_{R} d , Î^{3} is the friction parameter, Î^{2} is the inverse temperature, V is an external potential and_{M} is the mass matrix. The Langevin dynamics admits the Boltzmann-Gibbs measure Âµ â exp âÎ^{2}(V (q) + p_{T} M â1 p/2) as unique invariant distribution, and so it can be employed for calculating expectations of observables with respect to Âµ via time averages over_{a} long trajectory. When_{V} has several deep local minima, however, the Langevin dynamics is metastable, which leads to_{a} large asymptotic variance of the associated estimators. Note that the asymptotic variance of ergodic estimators can be expressed in terms of the solution to_{a} Poisson PDE involving the generator of the Markov semigroup corresponding to (4), and that stochastic dynamics of the type (4) are often analyzed through the associated Fokker-Planck and backward Kolmogorov PDEs. The Fokker-Planck equation associated with the dynamics (4), for instance, governs the time evolution of the law Ï of the random variable (q_{t} , p_{t}); it reads

â t Ï = LÏ, LÏ = â(M â1 p) â¢ â q Ï + â q_{V} (q) â¢ â p Ï + Î^{3}â p â¢ M â1 pÏ + Î^{2} â1 â p Ï .

Equations of this type play_{a} major role in the research directions presented below.

â¢ As_{a} first research direction, I propose to investigate whether using_{a} modification of (4) where_{V} is replaced_{V} + V , for some perturbation potential_{V} , can be useful for reducing the variance of ergodic estimators. A reweighting factor would then be incorporated in the expectation in order to ensure that the estimators are asymptotically unbiased. The motivation behind this surprisingly unexplored idea is that, by changing the drift appropriately, it is possible to increase the rate of convergence to equilibrium of the dynamics and reduce metastability issues, which provides scope for variance reduction. I will aim to determine, for example, the optimal choice of_{V} in order to minimize the asymptotic variance for_{a} given observable, or_{a} given class of observables. This approach may prove useful as_{a} general statistical method, with applications beyond molecular dynamics.

The calculation of transport coefficients is_{a} difficult problem in molecular dynamics: on the one hand, equilibrium approaches, which are based on the fact that transport coefficients can be expressed as time integrals of appropriate correlation functions (these are the Green-Kubo formulas), are plagued by relative statistical errors that diverge to infinity as the integration time increases. On the other hand, non-equilibrium approaches based on calculating the response of the system induced by_{a} small forcing suffer from large relative statistical errors, because the amplitude of the forcing needs to be small in order for the system to remain in the linear response regime. Although most practitioners of molecular dynamics recognize these difficulties, there have been very few attempts to develop bespoke variance reduction techniques. I discuss below two directions towards improving this state of affairs.

â¢ First, it would be interesting to study whether ideas from importance sampling can be leveraged for calculating transport coefficients more efficiently. More precisely, it may be the case that employing_{a} perturbed dynamics of the type (4) with_{V} replaced by_{V} + V could be useful for the calculation of transport coefficients through Green-Kubo's formula, an idea that received some attention in [7] . Since Green-Kubo's formula can be viewed as an expectation with respect to the law of the process, this change of the dynamics is corrected for by including_{a} Girsanov weight in the expectation. There are several natural mathematical questions related to this idea. First, can we optimally choose_{V} in order to minimize the variance of estimators of transport coefficients? In particular, there is_{a} trade-off between the decrease in the variance of estimators brought about by the faster convergence to equilibrium of the modified dynamics and_{a} possible increase in the variance due to the presence of the reweighting factors. Second, can the transport coefficients be obtained from the modified dynamics without recurring to Girsanov's weights, as these may prove too detrimental to the variance?

â¢ Second, I propose to study_{a} method for improving the estimators of transport coefficients relying on an external forcing and linear response. The motivating idea here is that there is some freedom in the choice of the external forcing applied to the system, which could be exploited in order to define better estimators. In particular, forcings that are not physically realizable, called synthetic forcings in [8] , may be employed. A natural question associated with this idea is the following: can we choose the forcing in such_{a} way that the region of validity of the linear response regime is broadened, which would enable to choose_{a} larger forcing in simulations in order to reduce the relative statistical error? Besides these two approaches, which are mid-term goals for this project, I emphasize that there are many other perspectives for more efficiently computing transport coefficients. In essence, many variance reduction techniques for computing averages with respect to probability measures have to be re-thought and extended to cover the case of properties such as transport coefficients, which include some dynamical information.

â¢ To conclude, I mention_{a} more theoretical research direction that we identified in [PStoV21] : can_{a} longtime convergence estimate to the stationary state be obtained for the generalized Langevin equation (GLE) through the direct_{L} 2 hypocoercivity framework developed in [12, 5, 6] , or through the more recent approach based on Schur complements introduced in [1]? The GLE, which was mentioned in section_{3}.2, is_{a} generalization of the Langevin dynamics (4) which, in the quasi-Markovian setting, can be recast as_{a} Markovian SDE over an extended phase space. Like the Langevin dynamics, it can be employed for sampling from the Boltzmann-Gibbs measure Âµ, but the study of convergence to equilibrium for the associated Fokker-Planck equation, in this case also_{a} hypoelliptic PDE, is more challenging because of the extra dimensions. The direct_{L} 2 hypocoercivity approach is attractive because it makes it easier to quantify convergence rates and, unlike the_{H} 1 approach employed in [PStoV21] , it tends to generalize well to numerical schemes for Fokker-Planck-type equations, such as spectral methods [21] .

Insertion within the Laboratoire de MathÃ©matiques AppliquÃ©es de CompiÃ¨gne (L.M.A.C.). I am convinced that my strong abilities in fields ranging from analysis and probability to numerical partial differential equations (PDEs) and computational stochastic dynamical systems will enable me to thrive within_{L}.M.A.C., and more specifically within the team on "ProblÃ¨mes Inverses et Analyse NumÃ©rique" (EPIA). My presence will both consolidate the team's position at the forefront of research in inverse problems and numerical analysis, and help it play_{a} leading role in emerging research concerning numerical methods for high-dimensional sampling, particularly in the context of Bayesian inverse problems. Within the EPIA team, I would be very enthusiastic to collaborate with Florian De Vuyst, with whom_{I} could work the development of parallel-in-time methods for time-dependent differential equations that can conserve symplectic properties, and also with Ahmad El Hajj who, being an expert in inverse problems and nonlinear/nonlocal PDEs, would be an ideal collaborator on the part of my research project that concerns the analysis of interacting particle methods for Bayesian inverse problems. Besides the research program above, I would very much enjoy the opportunity to develop collaborations with members of EPIA on other research projects stemming from real-world applications. My skills in mechanical engineering, which_{I} acquired in my master's degree, will prove useful for developing fruitful interactions with more applied laboratories within the university. I will also be enthusiastic to co-supervise PhD candidates in the laboratory, a task for which_{I} feel well-prepared in view of my extensive teaching and programming experience.

\end{document}