#!/usr/bin/env python3
"""Test script for subscript and superscript handling."""

from json_to_latex_converter import JSONToLaTeXConverter

def test_subscript_conversion():
    converter = JSONToLaTeXConverter()
    
    # Test cases
    test_cases = [
        # Original problematic case
        r"dq t = M \u22121 p t dt, dp t = \u2212\u2207 q V (q t ) dt \u2212 \u03b3M \u22121 p t dt + 2\u03b3\u03b2 \u22121 dW t .",
        
        # Other common patterns
        "X epsilon t",
        "Y epsilon t", 
        "q t",
        "p t",
        "dW t",
        "M^-1",
        "β^-1",
        "q t )",
        "V (q t )",
    ]
    
    print("Testing subscript and superscript conversion:")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}:")
        print(f"Input:  {test_case}")
        result = converter.convert_unicode_to_latex(test_case)
        print(f"Output: {result}")
        
        # For the main equation, also test the equation formatting
        if i == 1:
            equation = converter.format_equation(test_case, "4")
            print(f"As equation:\n{equation}")

if __name__ == "__main__":
    test_subscript_conversion()
