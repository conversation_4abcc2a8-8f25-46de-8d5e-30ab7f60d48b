import json
import re
import os
from typing import Dict, List, Any, Optional

def process_unicode_text(text: str) -> str:
    """
    Convert unicode characters and escape sequences to LaTeX equivalents
    """
    if not text:
        return ""
    
    # Dictionary for common unicode to LaTeX conversions
    unicode_to_latex_map = {
        # Greek letters
        '\u03b1': r'\alpha',
        '\u03b2': r'\beta', 
        '\u03b3': r'\gamma',
        '\u03b4': r'\delta',
        '\u03b5': r'\varepsilon',
        '\u03b6': r'\zeta',
        '\u03b7': r'\eta',
        '\u03b8': r'\theta',
        '\u03b9': r'\iota',
        '\u03ba': r'\kappa',
        '\u03bb': r'\lambda',
        '\u03bc': r'\mu',
        '\u03bd': r'\nu',
        '\u03be': r'\xi',
        '\u03bf': r'o',
        '\u03c0': r'\pi',
        '\u03c1': r'\rho',
        '\u03c3': r'\sigma',
        '\u03c4': r'\tau',
        '\u03c5': r'\upsilon',
        '\u03c6': r'\phi',
        '\u03c7': r'\chi',
        '\u03c8': r'\psi',
        '\u03c9': r'\omega',
        
        # Capital Greek letters
        '\u0391': r'A',
        '\u0392': r'B',
        '\u0393': r'\Gamma',
        '\u0394': r'\Delta',
        '\u0395': r'E',
        '\u0396': r'Z',
        '\u0397': r'H',
        '\u0398': r'\Theta',
        '\u0399': r'I',
        '\u039a': r'K',
        '\u039b': r'\Lambda',
        '\u039c': r'M',
        '\u039d': r'N',
        '\u039e': r'\Xi',
        '\u039f': r'O',
        '\u03a0': r'\Pi',
        '\u03a1': r'P',
        '\u03a3': r'\Sigma',
        '\u03a4': r'T',
        '\u03a5': r'\Upsilon',
        '\u03a6': r'\Phi',
        '\u03a7': r'X',
        '\u03a8': r'\Psi',
        '\u03a9': r'\Omega',
        
        # Mathematical symbols
        '\u2212': r'-',           # minus sign
        '\u2207': r'\nabla',      # nabla
        '\u221e': r'\infty',      # infinity
        '\u221d': r'\propto',     # proportional to
        '\u2208': r'\in',         # element of
        '\u2209': r'\notin',      # not element of
        '\u2211': r'\sum',        # summation
        '\u222b': r'\int',        # integral
        '\u2265': r'\geq',        # greater than or equal
        '\u2264': r'\leq',        # less than or equal
        '\u2260': r'\neq',        # not equal
        '\u2261': r'\equiv',      # equivalent
        '\u2245': r'\cong',       # approximately equal
        '\u2248': r'\approx',     # approximately
        '\u00b1': r'\pm',         # plus minus
        '\u00d7': r'\times',      # multiplication
        '\u00f7': r'\div',        # division
        '\u221a': r'\sqrt',       # square root
        '\u2202': r'\partial',    # partial derivative
        '\u2192': r'\rightarrow', # right arrow
        '\u2190': r'\leftarrow',  # left arrow
        '\u2194': r'\leftrightarrow', # bidirectional arrow
        '\u21d2': r'\Rightarrow', # double right arrow
        '\u21d0': r'\Leftarrow',  # double left arrow
        '\u21d4': r'\Leftrightarrow', # double bidirectional arrow
        '\u2200': r'\forall',     # for all
        '\u2203': r'\exists',     # there exists
        '\u2205': r'\emptyset',   # empty set
        '\u222a': r'\cup',        # union
        '\u2229': r'\cap',        # intersection
        '\u2286': r'\subseteq',   # subset or equal
        '\u2282': r'\subset',     # proper subset
        '\u2288': r'\not\subseteq', # not subset
        '\u2284': r'\not\subset', # not proper subset
        '\u2713': r'\checkmark',  # checkmark
        '\u2717': r'\times',      # cross mark
        
        # Other mathematical symbols
        '\u22c5': r'\cdot',       # center dot
        '\u00b7': r'\cdot',       # middle dot
        '\u2026': r'\ldots',      # ellipsis
        '\u2032': r"'",           # prime
        '\u2033': r"''",          # double prime
        '\u2034': r"'''",         # triple prime
        
        # Special characters
        '\u00a0': r'~',           # non-breaking space
        '\u2013': r'--',          # en dash
        '\u2014': r'---',         # em dash
        '\u201c': r'``',          # left double quotation mark
        '\u201d': r"''",          # right double quotation mark
        '\u2018': r'`',           # left single quotation mark
        '\u2019': r"'",           # right single quotation mark
    }
    
    # Apply unicode conversions
    for unicode_char, latex_equiv in unicode_to_latex_map.items():
        text = text.replace(unicode_char, latex_equiv)
    
    return text

def format_equation(raw_str: str, eq_num: Optional[str] = None) -> str:
    """
    Format equation string for LaTeX with proper mathematical notation
    """
    if not raw_str.strip():
        return ""
    
    # Process unicode characters first
    equation = process_unicode_text(raw_str)
    
    # Clean up spacing around mathematical operators
    equation = re.sub(r'\s*=\s*', ' = ', equation)
    equation = re.sub(r'\s*\+\s*', ' + ', equation)
    equation = re.sub(r'\s*-\s*', ' - ', equation)
    
    # Handle subscripts: "q t" -> "q_t", "p t" -> "p_t", etc.
    equation = re.sub(r'([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', equation)
    
    # Handle superscripts: "M −1" -> "M^{-1}", "β −1" -> "\beta^{-1}"
    equation = re.sub(r'([a-zA-Z\\}])\s*−\s*1\b', r'\1^{-1}', equation)
    equation = re.sub(r'([a-zA-Z\\}])\s*-\s*1\b', r'\1^{-1}', equation)
    
    # Handle function notation: "V (q t)" -> "V(q_t)"
    equation = re.sub(r'([a-zA-Z])\s*\(\s*([^)]+)\s*\)', r'\1(\2)', equation)
    
    # Handle differential notation: "d W t" -> "dW_t"
    equation = re.sub(r'd\s+([a-zA-Z])\s+([a-zA-Z0-9])', r'd\1_{\2}', equation)
    
    # Handle gradient notation: "∇ q" -> "\nabla_q"
    equation = re.sub(r'\\nabla\s+([a-zA-Z])', r'\\nabla_{\1}', equation)
    
    # Add proper spacing around commas in equations
    equation = re.sub(r',\s*', r', \\quad ', equation)
    
    # Handle dt and dW with proper spacing
    equation = re.sub(r'\s+dt\b', r' \\, dt', equation)
    equation = re.sub(r'\s+d([WX])', r' \\, d\1', equation)
    
    # Clean up extra spaces
    equation = re.sub(r'\s+', ' ', equation).strip()
    
    # Format as LaTeX equation
    if eq_num and eq_num.strip():
        # Remove parentheses from equation number if present
        eq_num = eq_num.strip('()')
        return f"\\[\n{equation}\n\\tag{{{eq_num}}}\n\\]"
    else:
        return f"\\[\n{equation}\n\\]"

def process_text_block(text: str) -> str:
    """
    Process regular text blocks, handling unicode and LaTeX escaping
    """
    if not text or text.strip() == "EQUATION":
        return ""
    
    # Process unicode characters
    text = process_unicode_text(text)
    
    # Handle citations in brackets like [CV20], [PStoV21], etc.
    text = re.sub(r'\[([A-Z][A-Za-z0-9]+)\]', r'\\cite{\1}', text)
    
    return text

def convert_grobid_json_to_latex(json_path: str, output_path: str = None) -> str:
    """
    Main function to convert GROBID JSON to LaTeX
    """
    
    # Generate output path if not provided
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(json_path))[0]
        output_path = f"{base_name}_converted.tex"
    
    # Load JSON data
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        raise ValueError(f"Error loading JSON file: {e}")
    
    # Start building LaTeX document
    latex_lines = [
        r"\documentclass{article}",
        r"\usepackage{amsmath,amssymb,mathtools}",
        r"\usepackage[utf8]{inputenc}",
        r"\usepackage{geometry}",
        r"\geometry{margin=1in}",
        r"\begin{document}",
        ""
    ]
    
    # Add title if available
    if 'title' in data and data['title']:
        title = process_unicode_text(data['title'])
        latex_lines.extend([
            f"\\title{{{title}}}",
            "\\maketitle",
            ""
        ])
    
    # Process body text from pdf_parse
    if 'pdf_parse' in data and 'body_text' in data['pdf_parse']:
        current_section = None
        
        for block in data['pdf_parse']['body_text']:
            # Handle section changes
            if block.get('section') and block['section'] != current_section:
                current_section = block['section']
                latex_lines.extend([
                    f"\\section{{{process_text_block(current_section)}}}",
                    ""
                ])
            
            # Handle equations
            if 'eq_spans' in block and block['eq_spans']:
                for eq in block['eq_spans']:
                    raw_str = eq.get('raw_str', '')
                    eq_num = eq.get('eq_num', '')
                    if raw_str.strip():
                        formatted_eq = format_equation(raw_str, eq_num)
                        latex_lines.extend([
                            formatted_eq,
                            ""
                        ])
            
            # Handle regular text
            elif 'text' in block and block['text'] and block['text'].strip() != "EQUATION":
                processed_text = process_text_block(block['text'])
                if processed_text.strip():
                    latex_lines.extend([
                        f"\\noindent {processed_text}",
                        ""
                    ])
    
    latex_lines.append(r"\end{document}")
    
    # Write to file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(latex_lines))
    
    print(f"✅ Conversion completed: {output_path}")
    return output_path

# Test with the specific equation from the user's example
if __name__ == "__main__":
    # Test the equation formatting function with the example
    test_equation = "dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t ."
    formatted = format_equation(test_equation, "(4)")
    print("Test equation formatting:")
    print(formatted)
    print("\n" + "="*50 + "\n")
    
    # Convert the actual file
    json_file = r"output_dir\mcf_analytical_presentation.json"
    if os.path.exists(json_file):
        try:
            output_file = convert_grobid_json_to_latex(json_file)
            print(f"📄 Generated: {output_file}")
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        print(f"❌ File not found: {json_file}")
