#!/usr/bin/env python3
"""
Example usage of the JSON to LaTeX converter
"""

from json_to_latex_converter import JSONToLaTeXConverter
import json

def test_equation_conversion():
    """Test the equation conversion functionality."""
    converter = JSONToLaTeXConverter()
    
    # Test the specific equation from your JSON
    test_equation = "∂ t ρ = Lρ, Lρ = −(M −1 p) • ∇ q ρ + ∇ q V (q) • ∇ p ρ + γ∇ p • M −1 pρ + β −1 ∇ p ρ ."
    
    print("Original equation:")
    print(test_equation)
    print("\nConverted to LaTeX:")
    converted = converter.convert_unicode_to_latex(test_equation)
    print(converted)
    print("\nFormatted as equation:")
    formatted = converter.format_equation(test_equation)
    print(formatted)

def convert_sample_json():
    """Convert a sample JSON structure to LaTeX."""
    converter = JSONToLaTeXConverter()
    
    # Sample JSON structure similar to GROBID output
    sample_json = {
        "title": "Sample Mathematical Document",
        "authors": [
            {"first": "<PERSON>", "last": "Doe"},
            {"first": "<PERSON>", "last": "<PERSON>"}
        ],
        "pdf_parse": {
            "abstract": [
                {
                    "text": "This is a sample abstract with mathematical notation: ∂u/∂t = ∇²u.",
                    "section": "Abstract"
                }
            ],
            "body_text": [
                {
                    "text": "Consider the differential equation:",
                    "section": "Introduction",
                    "sec_num": "1"
                },
                {
                    "text": "∂ t ρ = Lρ, Lρ = −(M −1 p) • ∇ q ρ + ∇ q V (q) • ∇ p ρ + γ∇ p • M −1 pρ + β −1 ∇ p ρ",
                    "section": "Introduction", 
                    "eq_spans": [
                        {
                            "raw_str": "∂ t ρ = Lρ, Lρ = −(M −1 p) • ∇ q ρ + ∇ q V (q) • ∇ p ρ + γ∇ p • M −1 pρ + β −1 ∇ p ρ",
                            "eq_num": "(1)"
                        }
                    ]
                },
                {
                    "text": "This equation describes the evolution of the density ρ in phase space.",
                    "section": "Introduction"
                }
            ]
        }
    }
    
    latex_output = converter.convert_json_to_latex(sample_json)
    
    print("Generated LaTeX document:")
    print("=" * 50)
    print(latex_output)
    
    # Save to file
    with open("sample_output.tex", "w", encoding="utf-8") as f:
        f.write(latex_output)
    print("\nSaved to sample_output.tex")

if __name__ == "__main__":
    print("Testing equation conversion:")
    print("=" * 50)
    test_equation_conversion()
    
    print("\n\nTesting full document conversion:")
    print("=" * 50)
    convert_sample_json()
