import json
import re

# 这里复用刚才那个转换函数
def unicode_to_latex(text):
    replacements = {
        '\u00b5': r'\mu',
        '\u03b1': r'\alpha',
        '\u03b2': r'\beta',
        '\u03b3': r'\gamma',
        '\u03b4': r'\delta',
        '\u03b5': r'\epsilon',
        '\u03b6': r'\zeta',
        '\u03b7': r'\eta',
        '\u03b8': r'\theta',
        '\u03b9': r'\iota',
        '\u03ba': r'\kappa',
        '\u03bb': r'\lambda',
        '\u03bc': r'\mu',
        '\u03bd': r'\nu',
        '\u03be': r'\xi',
        '\u03c0': r'\pi',
        '\u03c1': r'\rho',
        '\u03c3': r'\sigma',
        '\u03c4': r'\tau',
        '\u03c5': r'\upsilon',
        '\u03c6': r'\phi',
        '\u03c7': r'\chi',
        '\u03c8': r'\psi',
        '\u03c9': r'\omega',
        '\u2212': '-',      
        '\u221e': r'\infty',
        '\u2264': r'\leq',
        '\u2265': r'\geq',
        '\u221a': r'\sqrt{}',
        '\u221d': r'\propto',
        '\u00b7': r'\cdot',
        '\u2022': r'\cdot',
        '\u2260': r'\neq',
        '\u2261': r'\equiv',
        '\u2248': r'\approx',
        '\u2211': r'\sum',
        '\u222b': r'\int',
        '\u22c5': r'\cdot',
    }
    for uni_char, latex_cmd in replacements.items():
        text = text.replace(uni_char, latex_cmd)
    text = re.sub(r'([A-Za-z])\s*-\s*1', r'\1^{-1}', text)
    text = re.sub(r'([A-Za-z])\s*−\s*1', r'\1^{-1}', text)
    text = re.sub(r'([a-zA-Z0-9]+)\s+T\b', r'\1^{T}', text)
    text = re.sub(r'\bexp\b', r'\\exp', text)
    text = re.sub(r'([a-zA-Z])_([0-9]+)', r'\1_{\2}', text)
    text = re.sub(r'([a-zA-Z])\^([0-9a-zA-Z])', r'\1^{\2}', text)
    text = text.replace('*', r'\times ')
    return text

def process_grobid_json(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    for entry in data:
        if 'text' in entry:
            # 解码可能的unicode转义
            raw_text = entry['text']
            # 有的GROBID文本可能直接包含转义，需要先decode
            decoded_text = raw_text.encode('utf-8').decode('unicode_escape')
            entry['latex_text'] = unicode_to_latex(decoded_text)

    with open(output_path, 'w', encoding='utf-8') as f_out:
        json.dump(data, f_out, indent=2, ensure_ascii=False)

if __name__ == "__main__":
    input_json_path = r"D:\workspace\french_cv\output_dir\mcf_analytical_presentation.json"  # 改成你GROBID输出的json文件路径
    output_json_path = "grobid_output_latex.json"
    process_grobid_json(input_json_path, output_json_path)
    print("转换完成，结果已保存到", output_json_path)
