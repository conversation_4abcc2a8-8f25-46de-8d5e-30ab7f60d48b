# GROBID JSON to LaTeX Converter

A comprehensive Python tool for converting GROBID-generated JSON files to well-formatted LaTeX documents with proper mathematical notation.

## Features

- ✅ **Perfect equation formatting**: Converts complex mathematical equations to proper LaTeX syntax
- ✅ **Unicode to LaTeX conversion**: Handles Greek letters, mathematical symbols, and special characters
- ✅ **Citation handling**: Converts reference citations to LaTeX `\cite{}` commands
- ✅ **Section structure preservation**: Maintains document hierarchy and organization
- ✅ **Batch processing**: Convert multiple JSON files at once
- ✅ **Mathematical text enhancement**: Formats mathematical expressions in regular text

## Example Output

The converter transforms GROBID equations like this:

**Input (GROBID JSON):**
```json
"raw_str": "dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t ."
```

**Output (LaTeX):**
```latex
\[
dq_t = M^{-1} p_t \, dt, \quad dp_t = -\nabla_q V(q_t) \, dt - \gamma M^{-1} p_t \, dt + 2 \gamma \beta^{-1} \, dW_t.
\tag{4}
\]
```

## Installation

### Requirements
- Python 3.6+
- No external dependencies (uses only Python standard library)

### Download
Save the `grobid_json_to_latex_final.py` file to your working directory.

## Usage

### Basic Usage

#### Single File Conversion
```bash
python grobid_json_to_latex_final.py input.json output.tex
```

#### Auto-named Output
```bash
python grobid_json_to_latex_final.py input.json
# Creates input_converted.tex
```

#### Batch Conversion
```bash
python grobid_json_to_latex_final.py --batch input_directory output_directory
```

#### Quick Test
```bash
python grobid_json_to_latex_final.py
# Runs test with example equations and converts files in output_dir/
```

### Python API Usage

```python
from grobid_json_to_latex_final import convert_grobid_json_to_latex, batch_convert

# Convert single file
result = convert_grobid_json_to_latex("input.json", "output.tex")

# Batch convert directory
converted_files = batch_convert("input_directory", "output_directory")

# Test equation formatting
from grobid_json_to_latex_final import format_equation
equation = "dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t ."
latex_eq = format_equation(equation, "(4)")
print(latex_eq)
```

## Supported Conversions

### Mathematical Symbols
- Greek letters: α → `\alpha`, β → `\beta`, γ → `\gamma`, etc.
- Operators: ∇ → `\nabla`, ∈ → `\in`, ≥ → `\geq`, etc.
- Special symbols: ∞ → `\infty`, ∝ → `\propto`, ∑ → `\sum`, etc.

### Equation Formatting
- Subscripts: `q t` → `q_t`, `p t` → `p_t`
- Superscripts: `M −1` → `M^{-1}`, `β −1` → `\beta^{-1}`
- Function notation: `V (q t)` → `V(q_t)`
- Differential spacing: `dt` → `\, dt`, `dW t` → `\, dW_t`
- Comma separation: `,` → `, \quad`

### Text Processing
- Citations: `[CV20]` → `\cite{CV20}`
- Mathematical variables in text: `q t ∈ R d` → `$q_t \in \mathbb{R}^d$`
- Unicode characters in regular text

## Output Structure

The generated LaTeX document includes:

```latex
\documentclass{article}
\usepackage{amsmath,amssymb,mathtools}
\usepackage[utf8]{inputenc}
\usepackage{amsfonts}
\usepackage{geometry}
\usepackage{natbib}
\geometry{margin=1in}

% Document content with:
% - Title and abstract
% - Sections and subsections
% - Properly formatted equations
% - Text with mathematical notation
```

## File Structure

```
project/
├── grobid_json_to_latex_final.py  # Main converter
├── input.json                     # GROBID JSON file
├── output.tex                     # Generated LaTeX
└── output_dir/                    # Directory with JSON files
    ├── file1.json
    ├── file2.json
    └── ...
```

## Advanced Features

### Equation Numbering
- Automatically preserves equation numbers from GROBID
- Formats as `\tag{n}` for proper referencing

### Section Handling
- Maintains document hierarchy
- Converts section titles with proper escaping
- Handles subsection numbering

### Error Handling
- Graceful handling of malformed JSON
- Detailed error messages
- Continues processing other files in batch mode

## Customization

### Adding New Unicode Mappings
Edit the `unicode_map` dictionary in `process_unicode_text()`:

```python
unicode_map = {
    '\u03b1': r'\alpha',  # α
    '\u2208': r'\in',     # ∈
    # Add new mappings here
}
```

### Modifying Equation Formatting
Customize the `format_equation()` function for specific formatting needs:

```python
# Example: Change comma spacing
equation = re.sub(r',\s*', r', \\qquad ', equation)  # Uses \qquad instead of \quad
```

## Examples

### Command Line Examples

```bash
# Convert single file
python grobid_json_to_latex_final.py paper.json paper.tex

# Batch convert all JSON files in a directory
python grobid_json_to_latex_final.py --batch ./json_files ./latex_output

# Quick test and demo
python grobid_json_to_latex_final.py
```

### Integration Example

```python
import os
from grobid_json_to_latex_final import convert_grobid_json_to_latex

def process_papers(input_dir, output_dir):
    \"\"\"Process all JSON papers in a directory\"\"\"
    os.makedirs(output_dir, exist_ok=True)
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.json'):
            json_path = os.path.join(input_dir, filename)
            tex_name = filename.replace('.json', '.tex')
            tex_path = os.path.join(output_dir, tex_name)
            
            result = convert_grobid_json_to_latex(json_path, tex_path)
            if result:
                print(f"✅ Converted: {filename} → {tex_name}")

# Usage
process_papers('./grobid_output', './latex_papers')
```

## Troubleshooting

### Common Issues

1. **Unicode Encoding Errors**
   - Ensure input JSON files are UTF-8 encoded
   - The converter handles most unicode automatically

2. **Malformed JSON**
   - Check JSON syntax with a validator
   - Look for unescaped characters in strings

3. **LaTeX Compilation Issues**
   - Ensure all required packages are included
   - The generated LaTeX should compile with standard distributions

### Getting Help

For issues with specific equations or formatting, check:
1. The original GROBID JSON structure
2. The `format_equation()` function logic
3. Unicode mapping completeness

## License

This converter is provided as-is for academic and research use. Feel free to modify and extend for your specific needs.

## Contributing

To improve the converter:
1. Add new unicode symbol mappings
2. Enhance equation formatting rules
3. Improve text processing capabilities
4. Add support for additional GROBID features

## Version History

- **v1.0** - Initial release with basic conversion
- **v2.0** - Added perfect equation formatting
- **v3.0** - Enhanced batch processing and CLI interface
- **v4.0** - Final version with comprehensive unicode support
