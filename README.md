# 

first use @ pdf_json_universal.py , then 






# JSON to LaTeX Converter

A Python script to convert JSON files (especially from GROBID PDF parsing) to LaTeX format. This tool handles Unicode mathematical symbols and document structure conversion.

## Features

- **Unicode to LaTeX conversion**: Converts mathematical Unicode symbols (∂, ∇, ρ, γ, β, etc.) to proper LaTeX commands
- **Equation formatting**: Automatically formats equations with proper LaTeX equation environments
- **Document structure**: Preserves sections, abstracts, and document hierarchy
- **Metadata handling**: Includes title, authors, and other document metadata
- **Customizable output**: Options for document class and metadata inclusion

## Installation

No additional dependencies required beyond Python 3.6+. The script uses only standard library modules.

```bash
# Make the script executable (optional)
chmod +x json_to_latex_converter.py
```

## Usage

### Command Line Interface

```bash
# Basic usage
python json_to_latex_converter.py input.json

# Specify output file
python json_to_latex_converter.py input.json -o output.tex

# Skip metadata (title, authors)
python json_to_latex_converter.py input.json --no-metadata

# Use different document class
python json_to_latex_converter.py input.json --document-class report
```

### Python API

```python
from json_to_latex_converter import JSONToLaTeXConverter

# Create converter instance
converter = JSONToLaTeXConverter()

# Convert a file
converter.convert_file("input.json", "output.tex")

# Convert JSON data directly
import json
with open("input.json", "r") as f:
    data = json.load(f)

latex_content = converter.convert_json_to_latex(data)
print(latex_content)
```

## Examples

### Converting Mathematical Equations

The converter handles complex mathematical notation:

**Input JSON:**
```json
{
    "text": "∂ t ρ = Lρ, Lρ = −(M −1 p) • ∇ q ρ + ∇ q V (q) • ∇ p ρ + γ∇ p • M −1 pρ + β −1 ∇ p ρ .",
    "eq_spans": [
        {
            "raw_str": "∂ t ρ = Lρ, Lρ = −(M −1 p) • ∇ q ρ + ∇ q V (q) • ∇ p ρ + γ∇ p • M −1 pρ + β −1 ∇ p ρ",
            "eq_num": "(1)"
        }
    ]
}
```

**Output LaTeX:**
```latex
\begin{equation}
\partial t \rho = L\rho, L\rho = -(M^{-1} p) \cdot \nabla q \rho + \nabla q V (q) \cdot \nabla p \rho + \gamma\nabla p \cdot M^{-1} p\rho + \beta^{-1} \nabla p \rho
\label{eq:1}
\end{equation}
```

### Unicode Symbol Mapping

The converter includes comprehensive Unicode to LaTeX mapping:

| Unicode | Symbol | LaTeX Command |
|---------|--------|---------------|
| ∂ | Partial derivative | `\partial` |
| ∇ | Nabla | `\nabla` |
| ρ | Rho | `\rho` |
| γ | Gamma | `\gamma` |
| β | Beta | `\beta` |
| • | Bullet | `\cdot` |
| − | Minus | `-` |
| ⁻¹ | Superscript -1 | `^{-1}` |
| ∞ | Infinity | `\infty` |
| ≤ | Less than or equal | `\leq` |
| ≥ | Greater than or equal | `\geq` |
| ∈ | Element of | `\in` |
| → | Right arrow | `\rightarrow` |

## Document Structure

The converter processes GROBID JSON structure:

```json
{
    "title": "Document Title",
    "authors": [{"first": "John", "last": "Doe"}],
    "pdf_parse": {
        "abstract": [{"text": "Abstract text..."}],
        "body_text": [
            {
                "text": "Section content...",
                "section": "Introduction",
                "sec_num": "1",
                "eq_spans": [...]
            }
        ]
    }
}
```

## Generated LaTeX Structure

The output LaTeX document includes:

```latex
\documentclass{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{amsfonts}
\usepackage{mathtools}
\usepackage[margin=1in]{geometry}
\usepackage{hyperref}

\begin{document}

\title{Document Title}
\author{Author Names}
\maketitle

\begin{abstract}
Abstract content...
\end{abstract}

\section{Section Title}
Content with properly formatted equations...

\end{document}
```

## Testing

Run the example script to test functionality:

```bash
python example_usage.py
```

This will:
1. Test equation conversion with sample mathematical notation
2. Generate a complete LaTeX document from sample JSON
3. Save output to `sample_output.tex`

## Customization

### Adding New Unicode Symbols

To add support for additional Unicode symbols, modify the `unicode_to_latex` dictionary in the `JSONToLaTeXConverter` class:

```python
self.unicode_to_latex.update({
    '\u03c0': r'\pi',  # π
    '\u03c3': r'\sigma',  # σ
    # Add more mappings...
})
```

### Custom Document Classes

Supported document classes include:
- `article` (default)
- `report`
- `book`
- `amsart`
- `amsbook`

## Limitations

- Complex nested mathematical structures may require manual adjustment
- Some Unicode symbols might not be mapped (easily extensible)
- Document structure depends on GROBID JSON format
- Citations and references are preserved as text but not formatted as LaTeX citations

## Contributing

To contribute:
1. Add new Unicode symbol mappings
2. Improve equation parsing logic
3. Add support for additional JSON structures
4. Enhance error handling

## License

This script is provided as-is for educational and research purposes.
