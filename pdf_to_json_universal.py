#!/usr/bin/env python3

import os
import sys
import json
import argparse
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

# Add the s2orc-doc2json to the path
sys.path.insert(0, 's2orc-doc2json')

try:
    from doc2json.grobid2json.grobid.grobid_client import GrobidClient
    from doc2json.grobid2json.tei_to_json import convert_tei_xml_file_to_s2orc_json
    GROBID_AVAILABLE = True
except ImportError:
    print("Warning: GROBID not available. Will use alternative processing methods.")
    GROBID_AVAILABLE = False

class PDFToJSONConverter:
    """
    Universal PDF to JSON converter that handles documents with equations.
    Supports various document structures, not just academic papers.
    """
    
    def __init__(self, temp_dir="temp_dir", output_dir="output_dir"):
        self.temp_dir = temp_dir
        self.output_dir = output_dir
        self.ensure_directories()
        
    def ensure_directories(self):
        """Create necessary directories if they don't exist."""
        for directory in [self.temp_dir, self.output_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"Created directory: {directory}")
    
    def extract_math_expressions(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract mathematical expressions from text.
        Returns a list of math expressions with their positions and types.
        """
        math_expressions = []
        
        # Pattern for LaTeX-style math expressions
        latex_patterns = [
            (r'\$\$([^$]+)\$\$', 'display'),  # Display math
            (r'\$([^$]+)\$', 'inline'),       # Inline math
            (r'\\begin\{equation\*?\}(.*?)\\end\{equation\*?\}', 'display'),  # Equation environment
            (r'\\begin\{align\*?\}(.*?)\\end\{align\*?\}', 'display'),       # Align environment
            (r'\\begin\{eqnarray\*?\}(.*?)\\end\{eqnarray\*?\}', 'display'), # Eqnarray environment
        ]
        
        for pattern, math_type in latex_patterns:
            matches = re.finditer(pattern, text, re.DOTALL)
            for match in matches:
                math_expressions.append({
                    'content': match.group(1) if len(match.groups()) > 0 else match.group(0),
                    'type': math_type,
                    'start': match.start(),
                    'end': match.end(),
                    'raw': match.group(0)
                })
        
        return sorted(math_expressions, key=lambda x: x['start'])
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common PDF artifacts
        text = re.sub(r'\uf[0-9a-f]{3}', '', text)  # Remove Unicode private use area characters
        text = re.sub(r'[\x00-\x08\x0b-\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)  # Remove control characters
        
        return text
    
    def process_with_grobid(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Process PDF using GROBID and extract structured content."""
        if not GROBID_AVAILABLE:
            return None
            
        try:
            print("Processing with GROBID...")
            client = GrobidClient()
            
            # Get paper id as the name of the file
            paper_id = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
            tei_file = os.path.join(self.temp_dir, f'{paper_id}.tei.xml')
            
            # Process PDF through GROBID
            client.process_pdf(pdf_path, self.temp_dir, "processFulltextDocument")
            
            if not os.path.exists(tei_file):
                print(f"GROBID did not generate TEI file: {tei_file}")
                return None
            
            print(f"TEI file created: {tei_file} ({os.path.getsize(tei_file)} bytes)")
            
            # Convert TEI to structured format
            paper = convert_tei_xml_file_to_s2orc_json(tei_file)
            return paper.release_json()
            
        except Exception as e:
            print(f"Error processing with GROBID: {str(e)}")
            return None
    
    def fallback_text_extraction(self, pdf_path: str) -> Dict[str, Any]:
        """
        Fallback method for text extraction when GROBID is not available.
        Uses PyPDF2 or similar libraries for basic text extraction.
        """
        try:
            import PyPDF2
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                pages = []
                full_text = ""
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    cleaned_text = self.clean_text(page_text)
                    
                    pages.append({
                        'page_number': page_num + 1,
                        'text': cleaned_text,
                        'math_expressions': self.extract_math_expressions(cleaned_text)
                    })
                    
                    full_text += cleaned_text + "\n"
                
                return {
                    'extraction_method': 'PyPDF2',
                    'pages': pages,
                    'full_text': full_text,
                    'total_pages': len(pages),
                    'math_expressions': self.extract_math_expressions(full_text)
                }
                
        except ImportError:
            print("PyPDF2 not available. Installing...")
            os.system("pip install PyPDF2")
            return self.fallback_text_extraction(pdf_path)
        except Exception as e:
            print(f"Error with fallback extraction: {str(e)}")
            return {
                'extraction_method': 'failed',
                'error': str(e),
                'pages': [],
                'full_text': "",
                'total_pages': 0,
                'math_expressions': []
            }
    
    def process_structured_content(self, grobid_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process GROBID output to enhance equation handling."""
        processed_data = grobid_data.copy()
        
        # Extract all math expressions from different sections
        all_math = []
        
        # Process abstract
        if 'abstract' in processed_data and processed_data['abstract']:
            abstract_math = self.extract_math_expressions(processed_data['abstract'])
            for expr in abstract_math:
                expr['section'] = 'abstract'
            all_math.extend(abstract_math)
        
        # Process body text
        if 'body_text' in processed_data:
            for i, section in enumerate(processed_data['body_text']):
                if 'text' in section:
                    section_math = self.extract_math_expressions(section['text'])
                    for expr in section_math:
                        expr['section'] = f"body_section_{i}"
                        expr['section_title'] = section.get('section', 'Unknown')
                    all_math.extend(section_math)
        
        # Add enhanced math analysis
        processed_data['math_analysis'] = {
            'total_expressions': len(all_math),
            'inline_count': len([m for m in all_math if m['type'] == 'inline']),
            'display_count': len([m for m in all_math if m['type'] == 'display']),
            'expressions': all_math
        }
        
        return processed_data
    
    def convert_pdf_to_json(self, pdf_path: str, output_filename: Optional[str] = None) -> str:
        """
        Main conversion method that handles any PDF with equations.
        
        Args:
            pdf_path: Path to the input PDF file
            output_filename: Optional custom output filename
            
        Returns:
            Path to the generated JSON file
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        print(f"Converting PDF to JSON: {pdf_path}")
        
        # Generate output filename
        if output_filename is None:
            base_name = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
            output_filename = f"{base_name}_converted.json"
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        # Try GROBID first (best for academic content with equations)
        structured_data = None
        if GROBID_AVAILABLE:
            structured_data = self.process_with_grobid(pdf_path)
        
        if structured_data:
            print("Successfully processed with GROBID")
            # Enhance the structured data with additional math analysis
            final_data = self.process_structured_content(structured_data)
            final_data['processing_method'] = 'grobid_enhanced'
        else:
            print("Using fallback text extraction method")
            # Use fallback method
            final_data = self.fallback_text_extraction(pdf_path)
            final_data['processing_method'] = 'fallback'
        
        # Add metadata
        final_data['metadata'] = {
            'source_file': os.path.abspath(pdf_path),
            'processing_date': datetime.now().isoformat(),
            'converter_version': '1.0',
            'file_size_bytes': os.path.getsize(pdf_path)
        }
        
        # Write to JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, indent=2, ensure_ascii=False, sort_keys=False)
        
        print(f"JSON file created: {output_path}")
        print(f"Output file size: {os.path.getsize(output_path)} bytes")
        
        # Print summary
        self.print_conversion_summary(final_data)
        
        return output_path
    
    def print_conversion_summary(self, data: Dict[str, Any]):
        """Print a summary of the conversion results."""
        print("\n" + "="*50)
        print("CONVERSION SUMMARY")
        print("="*50)
        
        print(f"Processing method: {data.get('processing_method', 'unknown')}")
        
        if 'title' in data and data['title']:
            print(f"Document title: {data['title']}")
        
        if 'abstract' in data and data['abstract']:
            print(f"Abstract length: {len(data['abstract'])} characters")
        
        if 'math_analysis' in data:
            math_info = data['math_analysis']
            print(f"Mathematical expressions found: {math_info['total_expressions']}")
            print(f"  - Inline expressions: {math_info['inline_count']}")
            print(f"  - Display expressions: {math_info['display_count']}")
        
        if 'math_expressions' in data:
            print(f"Mathematical expressions found: {len(data['math_expressions'])}")
        
        if 'body_text' in data:
            print(f"Body text sections: {len(data['body_text'])}")
        
        if 'pages' in data:
            print(f"Total pages processed: {len(data['pages'])}")
        
        print("="*50)

def main():
    parser = argparse.ArgumentParser(description='Convert PDF with equations to JSON format')
    parser.add_argument('pdf_path', help='Path to the input PDF file')
    parser.add_argument('-o', '--output', help='Output JSON filename (optional)')
    parser.add_argument('--temp-dir', default='temp_dir', help='Temporary directory for processing')
    parser.add_argument('--output-dir', default='output_dir', help='Output directory for JSON files')
    
    args = parser.parse_args()
    
    try:
        converter = PDFToJSONConverter(temp_dir=args.temp_dir, output_dir=args.output_dir)
        output_path = converter.convert_pdf_to_json(args.pdf_path, args.output)
        
        print(f"\nConversion completed successfully!")
        print(f"Output file: {output_path}")
        
    except Exception as e:
        print(f"Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # If run without arguments, use the current test file
    if len(sys.argv) == 1:
        test_pdf = r"D:\workspace\french_cv\mcf_analytical_presentation.pdf"
        if os.path.exists(test_pdf):
            converter = PDFToJSONConverter()
            converter.convert_pdf_to_json(test_pdf)
        else:
            print("Test PDF not found. Please provide a PDF file path as argument.")
    else:
        main()
