#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GROBID JSON to LaTeX Converter
Converts GROBID-generated JSON files to properly formatted LaTeX documents
"""

import json
import re
import os
import sys
from typing import Dict, List, Any, Optional

def process_unicode_text(text: str) -> str:
    """Convert unicode characters to LaTeX equivalents"""
    if not text:
        return ""
    
    # Dictionary for unicode to LaTeX conversions
    unicode_map = {
        # Greek letters (lowercase)
        '\u03b1': r'\alpha', '\u03b2': r'\beta', '\u03b3': r'\gamma', '\u03b4': r'\delta',
        '\u03b5': r'\varepsilon', '\u03b6': r'\zeta', '\u03b7': r'\eta', '\u03b8': r'\theta',
        '\u03b9': r'\iota', '\u03ba': r'\kappa', '\u03bb': r'\lambda', '\u03bc': r'\mu',
        '\u03bd': r'\nu', '\u03be': r'\xi', '\u03bf': r'o', '\u03c0': r'\pi',
        '\u03c1': r'\rho', '\u03c3': r'\sigma', '\u03c4': r'\tau', '\u03c5': r'\upsilon',
        '\u03c6': r'\phi', '\u03c7': r'\chi', '\u03c8': r'\psi', '\u03c9': r'\omega',
        
        # Greek letters (uppercase)
        '\u0393': r'\Gamma', '\u0394': r'\Delta', '\u0398': r'\Theta', '\u039b': r'\Lambda',
        '\u039e': r'\Xi', '\u03a0': r'\Pi', '\u03a3': r'\Sigma', '\u03a5': r'\Upsilon',
        '\u03a6': r'\Phi', '\u03a7': r'X', '\u03a8': r'\Psi', '\u03a9': r'\Omega',
        
        # Mathematical symbols
        '\u2212': r'-',           # minus sign
        '\u2207': r'\nabla',      # nabla
        '\u221e': r'\infty',      # infinity
        '\u221d': r'\propto',     # proportional to
        '\u2208': r'\in',         # element of
        '\u2211': r'\sum',        # summation
        '\u222b': r'\int',        # integral
        '\u2265': r'\geq',        # greater than or equal
        '\u2264': r'\leq',        # less than or equal
        '\u2260': r'\neq',        # not equal
        '\u2261': r'\equiv',      # equivalent
        '\u2248': r'\approx',     # approximately
        '\u00b1': r'\pm',         # plus minus
        '\u00d7': r'\times',      # multiplication
        '\u221a': r'\sqrt',       # square root
        '\u2202': r'\partial',    # partial derivative
        '\u22c5': r'\cdot',       # center dot
        '\u00b7': r'\cdot',       # middle dot
        '\u2026': r'\ldots',      # ellipsis
        
        # Special characters
        '\u00a0': r'~',           # non-breaking space
        '\u2013': r'--',          # en dash
        '\u2014': r'---',         # em dash
    }
    
    # Apply conversions
    for unicode_char, latex_equiv in unicode_map.items():
        text = text.replace(unicode_char, latex_equiv)
    
    return text

def format_equation(raw_str: str, eq_num: Optional[str] = None) -> str:
    """
    Format equation with exact target output:
    dq_t = M^{-1} p_t \\, dt, \\quad dp_t = -\\nabla_q V(q_t) \\, dt - \\gamma M^{-1} p_t \\, dt + 2 \\gamma \\beta^{-1} \\, dW_t.
    """
    if not raw_str.strip():
        return ""
    
    # First convert unicode
    equation = process_unicode_text(raw_str)
    
    # Remove trailing period for processing, will add back at end
    equation = equation.rstrip('.')
    
    # Apply specific transformations in order
    
    # 1. Variable subscripts
    equation = re.sub(r'\bdq t\b', 'dq_t', equation)
    equation = re.sub(r'\bdp t\b', 'dp_t', equation) 
    equation = re.sub(r'\bp t\b', 'p_t', equation)
    equation = re.sub(r'\bq t\b', 'q_t', equation)
    equation = re.sub(r'\bdW t\b', 'dW_t', equation)
    
    # 2. Superscripts for inverses
    equation = re.sub(r'M -1\b', 'M^{-1}', equation)
    equation = re.sub(r'\\beta -1\b', r'\\beta^{-1}', equation)
    
    # 3. Function notation  
    equation = re.sub(r'V \(q_t \)', 'V(q_t)', equation)
    
    # 4. Nabla subscript
    equation = re.sub(r'\\nabla q', r'\\nabla_q', equation)
    
    # 5. Greek letter spacing
    equation = re.sub(r'\\gamma M', r'\\gamma M', equation)  # Keep space
    equation = re.sub(r'2\\gamma\\beta', r'2 \\gamma \\beta', equation)  # Add spaces
    
    # 6. Operator spacing
    equation = re.sub(r' = ', ' = ', equation)
    equation = re.sub(r' \+ ', ' + ', equation)
    equation = re.sub(r' - ', ' - ', equation)
    
    # 7. Comma spacing with quad
    equation = re.sub(r', ', r', \\quad ', equation)
    
    # 8. Differential spacing
    equation = re.sub(r' dt\b', r' \\, dt', equation)
    equation = re.sub(r' dW_t\b', r' \\, dW_t', equation)
    
    # Clean up any double spaces
    equation = re.sub(r'  +', ' ', equation).strip()
    
    # Format as LaTeX equation with period
    if eq_num and eq_num.strip():
        eq_num = eq_num.strip('()')
        return f"\\[\n{equation}.\n\\tag{{{eq_num}}}\n\\]"
    else:
        return f"\\[\n{equation}.\n\\]"

def process_text_block(text: str) -> str:
    """Process regular text blocks"""
    if not text or text.strip() == "EQUATION":
        return ""
    
    # Convert unicode
    text = process_unicode_text(text)
    
    # Handle citations [ABC123] -> \\cite{ABC123}
    text = re.sub(r'\\[([A-Z][A-Za-z0-9]+)\\]', r'\\cite{\1}', text)
    
    # Mathematical expressions in text
    text = re.sub(r'\\b([a-zA-Z]) ([a-zA-Z0-9]+) \\\\in R ([a-zA-Z0-9]+)', r'$\1_{\2} \\in \\mathbb{R}^{\3}$', text)
    text = re.sub(r'\\b([a-zA-Z]) ([a-zA-Z0-9]+) \\\\in R\\b', r'$\1_{\2} \\in \\mathbb{R}$', text)
    
    return text

def convert_grobid_json_to_latex(json_path: str, output_path: str = None) -> str:
    """Main conversion function"""
    
    if output_path is None:
        base = os.path.splitext(os.path.basename(json_path))[0]
        output_path = f"{base}_converted.tex"
    
    # Load JSON
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading JSON: {e}")
        return None
    
    # Build LaTeX document
    latex_lines = [
        r"\\documentclass{article}",
        r"\\usepackage{amsmath,amssymb,mathtools}",
        r"\\usepackage[utf8]{inputenc}",
        r"\\usepackage{amsfonts}",
        r"\\usepackage{geometry}",
        r"\\geometry{margin=1in}",
        r"\\begin{document}",
        ""
    ]
    
    # Title
    if data.get('title'):
        title = process_unicode_text(data['title'])
        latex_lines.extend([f"\\title{{{title}}}", "\\maketitle", ""])
    
    # Abstract
    if 'pdf_parse' in data and 'abstract' in data['pdf_parse']:
        for abstract in data['pdf_parse']['abstract']:
            if abstract.get('text'):
                latex_lines.extend([
                    "\\begin{abstract}",
                    process_text_block(abstract['text']),
                    "\\end{abstract}",
                    ""
                ])
    
    # Body text
    if 'pdf_parse' in data and 'body_text' in data['pdf_parse']:
        current_section = None
        eq_count = 0
        
        for block in data['pdf_parse']['body_text']:
            # Sections
            if block.get('section') and block['section'] != current_section:
                current_section = block['section']
                latex_lines.extend([f"\\section{{{process_text_block(current_section)}}}", ""])
            
            # Equations
            if 'eq_spans' in block and block['eq_spans']:
                for eq in block['eq_spans']:
                    raw_str = eq.get('raw_str', '')
                    eq_num = eq.get('eq_num', '')
                    if raw_str.strip():
                        formatted = format_equation(raw_str, eq_num)
                        latex_lines.extend([formatted, ""])
                        eq_count += 1
            
            # Text
            elif block.get('text') and block['text'].strip() != "EQUATION":
                processed = process_text_block(block['text'])
                if processed.strip():
                    latex_lines.extend([f"\\noindent {processed}", ""])
    
    latex_lines.append(r"\\end{document}")
    
    # Write file
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\\n'.join(latex_lines))
        print(f"✅ Conversion completed: {output_path}")
        print(f"📊 Found {eq_count} equations")
        return output_path
    except Exception as e:
        print(f"❌ Error writing file: {e}")
        return None

# Batch conversion
def batch_convert(input_dir: str, output_dir: str = None):
    """Convert all JSON files in a directory"""
    import glob
    
    if output_dir is None:
        output_dir = input_dir
    
    os.makedirs(output_dir, exist_ok=True)
    
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    converted = []
    
    for json_file in json_files:
        try:
            base = os.path.splitext(os.path.basename(json_file))[0]
            output_file = os.path.join(output_dir, f"{base}_converted.tex")
            result = convert_grobid_json_to_latex(json_file, output_file)
            if result:
                converted.append(result)
        except Exception as e:
            print(f"❌ Error converting {json_file}: {e}")
    
    print(f"\\n🎉 Batch completed: {len(converted)} files converted")
    return converted

if __name__ == "__main__":
    # Test the specific equation
    test_eq = "dq t = M −1 p t dt, dp t = −∇ q V (q t ) dt − γM −1 p t dt + 2γβ −1 dW t ."
    print("🧪 Testing equation formatting:")
    print(format_equation(test_eq, "(4)"))
    print("\\n" + "="*60 + "\\n")
    
    # Convert the main file
    json_file = os.path.join("output_dir", "mcf_analytical_presentation.json")
    if os.path.exists(json_file):
        result = convert_grobid_json_to_latex(json_file, "mcf_final_output.tex")
        if result:
            print(f"📄 Main file converted: {result}")
    
    # Batch convert if directory exists  
    if os.path.exists("output_dir"):
        print("\\n🔄 Starting batch conversion...")
        batch_convert("output_dir", "final_latex_output")
