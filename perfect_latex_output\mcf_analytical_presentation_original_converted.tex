\documentclass{article}
\usepackage{amsmath,amssymb,mathtools}
\usepackage[utf8]{inputenc}
\usepackage{amsfonts}
\usepackage{geometry}
\geometry{margin=1in}
\begin{document}

\title{Présentation analytique Urbain Vaes November 3, 2022 La structure de ce document est la suivante: les premières pages contiennent un bref curriculum vitae et une liste de mes contributions. En section 2, mon activité de recherche antérieure est résumée brièvement et structurée selon trois catégories principales. Mes contributions majeures relatives à chacune de ces catégories sont ensuite détaillées en section 3. Finalement, en section 4, mon programme de recherche à court et moyen termes est présenté. 1 UrbainVaesCurriculumVitae6et8avenueBlaisePascalCitéDescartes-ChampssurMarne77455MarnelaVallé projetMATHERIALS,Inria,France-Titreduprojetderecherche: Échantillonnageengrandedimension:del'analy 2021Chercheurpostdoctorant,équipeMATHERIALS,InriaetCERMICS( ÉcoledesPonts),France-Encadrantp Uni-Encadrants}
\maketitle

\noindent Au cours de ma recherche, j'ai participé au développement des codes mathématiques suivants, qui ont été écrits dans le but soit de valider des résultats théoriques, soit d'illustrer des méthodes numériques, soit d'analyser des systèmes physiques ou mathématiques.

\noindent • Hermipy: https://github.com/urbainvaes/hermipy. Hermipy est une bibliothèque Python qui permet d'automatiser la plupart des opérations impliquées dans l'implémentation de méthodes spectrales de type Fourier/Hermite. La bibliothèque utilise les structures de données fournies par la bibliothèque NumPy, et elle dépend aussi de SymPy pour les calculs symboliques. Les calculs intensifs sont pris en charge par un composant compilé en C++, avec lequel la bibliothèque Python communique grâce à Boost. La bibliothèque Hermipy a été développée et utilisée pour produire des résultats numériques dans trois de mes publications.

\noindent • Éléments finis pour l'équation de Cahn-Hilliard : https://github.com/urbainvaes/cahnhilliard Ce code permet de résoudre avec FreeFem++ l'équation de Cahn-Hilliard avec conditions aux fron-tières non-homogènes par les méthodes numériques semi-implicites étudiées dans \cite{AVPK19}. Il offre aux utilisateurs les moyens de rapidement définir les conditions initiales et conditions aux limites du problème, d'exécuter une méthode numérique particulière avec des paramètres appropriés, et de visualiser la solution. Les cas bidimensionnel et tridimensionnel sont supportés, et la géométrie du domaine ainsi que le maillage peuvent être définis grâce au logiciel Gmsh.

\noindent • Bayesicle: https://github.com/urbainvaes/bayesicle Bayesicle est une bibliothèque Python en cours de développement mettant en oeuvre plusieurs méth-odes basées sur des systèmes de particules en interaction pour l'échantillonnage et l'optimisation, en particulier dans le contexte des problèmes inverses bayésiens. La bibliothèque implémente les méthodes d'optimisation et d'échantillonnage basées sur le consensus, les méthodes de type Kalman d'ensemble, ainsi que d'autres méthodes développées et étudiées dans ma recherche. 5

\noindent • Langevin-self-diffusion: https://github.com/urbainvaes/Langevin-self-diffusion Ceci est une collection de scripts en Julia implémentant une technique de réduction de variance basée sur les variables de contrôle pour le calcul du coefficient de diffusion effectif associé à la dynamique de Langevin. Le code sert de preuve de concept dans mon travail postdoctoral actuel avec Gabriel Soltz.

\noindent • Jupyter notebooks pour l'enseignement des méthodes numériques pour les processus stochastiques: https://github.com/urbainvaes/computational s tochastic p rocesses Ces codes Python ont été écrits pour illustrer les méthodes vues au cours Computational Stochastic Processes, que j'ai enseigné à Imperial College London pendant le trimestre de printemps en 2020. Ils implémentent des méthodes standards pour générer des variables aléatoires, plusieurs approches pour réduire la variance d'estimateurs de type Monte Carlo, les méthodes numériques de base pour la simulation des équations différentielles stochastiques, et quelques techniques d'inférence bayésienne. Les Jupyter notebooks peuvent être visualisés en format HTML sur ma page web, dans la section Python examples.

\noindent En plus de ces contributions, j'ai développé pendant mon temps libre des logiciels utilitaires non mathé-matiques que beaucoup de programmeurs utilisent quotidiennement, et qui sont accessibles sur mon profil Github. Toutes mes contributions logicielles sont accessibles sous licences libres.

\noindent 2 Brief summary of my past research activity Most of my past activity is at the interface between stochastic differential equations (SDEs), partial differen-tial equations (PDEs), Bayesian statistics and numerical methods. Many of the problems I am interested in are stochastic in nature, but I often approach them by means of PDE methods, for example via Kolmogorov backward and forward equations. My main research contributions can be divided in three main categories:

\noindent Analysis and simulation of systems with multiple scales. Multiscale modeling was one of the main themes of my PhD research. I studied, in separate works, systems with multiple time scales and problems with multiple length scales.

\noindent In a collaboration with Grigorios Pavliotis and Assyr Abdulle, I developed and analyzed an efficient method for solving numerically systems of SDEs with multiple time scales. Multiscale stochastic systems arise frequently in applications, for example in materials science and climate modeling. When the scale separation is clear, the evolution of the slow variables is generally accurately described by a closed effective equation which, depending on the problem, can be obtained by averaging or homogenization of the mul-tiscale system. Our aim was to propose a deterministic spectral method for efficiently calculating, in the homogenization setting, the drift and diffusion coefficients appearing in this equation.

\noindent Later in my PhD, I had the opportunity to conduct research in the Department of Chemical Engineering under the supervision of Serafim Kalliadasis, where I developed, in collaboration with Serafim and other coworkers, a novel finite element method for the Cahn-Hilliard equation with wetting. The Cahn-Hilliard equation is a fourth order parabolic equation that is widely used for modeling immiscible binary mixtures via a phase field approach. It contains a small parameter encoding the typical interface width which, in general, is much smaller than the characteristic length of the domain. Our main contribution was to extend a class of linear, semi-implicit finite element schemes from the case of a homogeneous boundary condition to that of a cubic boundary condition for the phase field. We also proposed a mesh adaptation strategy that enables to accurately capture the interfacial regions, and we implemented the methods in Freefem++.

\noindent Study of dynamical systems with colored noise or memory. In my last year of PhD, together with Grigorios Pavliotis and Susana Gomes, I studied meanfield limits for systems of weakly interacting diffusion processes. Systems of this type appear in a wide variety of applications, ranging from molecular dynamics to the social sciences and machine learning, and they are known to exhibit rich dynamics. Our original contribution was to consider noise processes that are not delta-correlated in time. Noise models of this type, known as colored noise models, enable a more realistic representation of physical systems but their analysis presents additional challenges compared to the case of white noise. Our main aim was to scrutinize the effect of the correlation time of noise on the number and stability of the steady states of the nonlocal equation of Fokker-Planck type that arises in the mean-field limit, in order to better understand the phase transitions exhibited by the particle system. 6 Later, in collaboration with Gabriel Stoltz and Grigorios Pavliotis, I studied scaling limits for the rate of convergence to equilibrium and the effective diffusion coefficient of generalized Langevin dynamics. These dynamics were initially proposed as a model for describing the motion of a particle interacting with a heat bath at equilibrium, and they now have applications in many other areas of science and engineering, ranging from polymer dynamics to sampling and global optimization. They can be viewed as Langevin dynamics driven by colored noise and they can be recast, in the quasi-Markovian setting, as a system of SDEs on an extended phase space. Our main objective was to obtain more quantitative bounds on the rate of convergence to equilibrium than previously available, which provided the means for deriving new asymptotic results for the effective diffusion coefficient associated with the dynamics in the periodic setting.

\noindent As a postdoctoral researcher in the MATHERIALS team since November 2020, I collaborate with Gabriel Stoltz on the study of variance reduction approaches for the calculation of transport coefficients in molecular dynamics. Our first project concerns the development of a variance reduction method based on control variates for calculating the mobility of two-dimensional Langevin dynamics in a periodic potential. The aim is to shed light on the behavior of the mobility in the underdamped limit which, except in dimension 1, is not currently well understood by the scientific community.

\noindent Derivative-free methods for Bayesian inference. When I was a postdoctoral researcher at Imperial College London, my research focused in large part on the analysis and development of gradient-free methods for solving inverse problems, specifically for the tasks of minimizing a regularized misfit or sampling from a Bayesian posterior distribution. In many applications of inverse problems, the forward model is expensive to evaluate and its derivatives are not available or too costly to obtain, so gradient-free methods that require few evaluations of the forward model are an attractive proposition. In recent years, two important developments opened the door to promising research avenues in this area. Firstly, the ensemble Kalman filter was extended from state estimation problems to inverse problems, initially for misfit minimization and later for posterior sampling. The resulting gradient-free methods are proving empirically very successful, partly because they are based on interacting particle systems and can therefore leverage recent advances in parallel computing, but they still lack a robust theoretical footing. Secondly, a trend of studying numerical algorithms based on interacting replicas from a mean-field viewpoint emerged from the optimization community and brought considerable insight, particularly into the behaviour of consensus-based optimization methods. The mean-field viewpoint holds considerable promise for improving our understanding of interacting particle algorithms. My first contribution in this area was the derivation, in collaboration with José Carrillo, of a sharp contraction estimate for a mean-field Fokker-Planck equation associated with an ensemble Kalman-based method for posterior sampling. I then collaborated with Grigorios Pavliotis and Andrew Stuart on the development of a sampling method based on a multiscale dynamics that can be refined systematically to return the true solution, thereby overcoming the issue of uncontrolled gradient approximations affecting ensemble Kalman-based methods. More recently, in ongoing work with José Carrillo, Franca Hoffmann and Andrew Stuart that is almost completed, we are investigating whether ideas from consensus-based methods, which are proving to be interesting and viable for optimization, can be useful also for the purpose of sampling. As a first step in this direction, we are scrutinizing the convergence properties of the nonlocal Fokker-Planck equation associated with the method. 7 3 Summary of main contributions 3.1 Spectral methods for multiscale stochastic differential equations Description of the contribution. This work focused on the development and analysis, through rigor-ous proofs and numerical experiments, of a spectral method based on Hermite polynomials for multiscale stochastic equations. More precisely, we developed a numerical method for solving through a homogenization approach multiscale stochastic differential equations (SDEs) of the type

\noindent dX t = t , Y t ) dt + dY t = t , Y t ) dt + f (X 1 1 2 h(X 2 x dWx(t), 2 y dWy(t). t Rm, Y

\noindent t Rn, and is a small parameter encoding the timescale separation between the Here X processes X and Y . The parameters x Rm\timesm, y Rn\timesn are constant matrices, and Wx, Wy are independent m and n-dimensional Brownian motions, respectively. In order for this system to have a well-defined limit as → 0, it is assumed for all x Rm that the equation for Y t admits a unique invariant when X measure x t = x is taken as a fixed parameter, and that the slow drift f (x, • ) has mean 0 with respect x .

\noindent When 1, traditional methods for the numerical solution of SDEs are not suitable for this problem, because the time step required to perform a stable and accurate time integration would be prohibitively small. It is well-known, however, that the slow process X converges weakly in C([0, T ]), in the as limit → 0, to the solution of a simpler effective equation, dXt = F (Xt) dt + A(Xt) dW (t),

\noindent where W is a standard Brownian motion on Rm, in general different from Wx and Wy. The effective drift and diffusion coefficients, F and A, can be expressed in terms of the solution to a Poisson partial differential equation:

\[
L0(x) (x, \quad y) = f (x, \quad y). (.
\tag{2}
\]

\noindent )

\noindent Here the operator L0(x) is, up to the factor 2, the generator of the fast dynamics when X t = x is viewed as a fixed parameter. The Poisson equation ( 2) should be viewed as a partial differential equation (PDE) on the state-space Rn of Y t , with x only playing the role of a fixed parameter. When is sufficiently small, the slow dynamics X t can be well approximated by solving the effective equation ( 1) instead of the full multiscale system. To this end, we proposed to use a spectral method based on Hermite polynomials in order to calculate an approximate solution to the Poisson equation (2) , which can then be employed in order to calculate the effective coefficients F and A of the homogenized equation. We proved error estimates showing the super-algebraic convergence of the spectral method under appropriate smoothness and decay assumptions, and we provided numerical evidence for the efficiency of the method when the dimension n of the state-space of Y is sufficiently small. A comparison with existing methods and an application to t singularly perturbed stochastic PDEs were also presented.

\noindent Collaborators. This work was a collaboration with Grigorios Pavliotis (Maths, Imperial College) and Assyr Abdulle (Maths, EPFL).

\noindent Originality and impact. The originality of our work lies in the fact that, in contrast with averaging-based methods, such as the heterogeneous multiscale method or the equation-free method, which rely on Monte Carlo simulations, our method is based on a deterministic spectral method, providing better efficiency in small dimensions.

\noindent Dissemination. This work led to the publication \cite{APV17} and to the development of a Python/C++ library for Hermite and Fourier/Hermite spectral methods, called Hermipy. The associated code is freely available on Github. 8 3.2 Study of dynamical systems with colored noise or memory Description of the contribution. Time-correlated noise, also known as colored noise, is often employed as a realistic alternative to white noise in models of physical systems. Recently, dynamical systems with colored noise, and more generally those with inertia or memory, have proven useful also in the context of sampling and optimization, including for applications in the big-data and machine-learning settings. Two of my research works aimed at improving our understanding of systems of this type.

\noindent • Together with Susana Gomes and Grigorios Pavliotis, we studied systems of particles driven by colored noise and interacting via a quadratic Curie-Weiss potential. In the case of white noise, particle systems of this type are known to exhibit rich dynamics, and in particular one or several phase transitions, when the external potential V has several local minima. Our main aim was to study the effect of colored noise on phase transitions in the case of the standard double-well potential. More precisely, we studied the effect of the correlation time of the noise on the bifurcation diagram for the steady states of the nonlocal Fokker-Planck equation that governs the evolution of the particle system in the mean-field limit, with the inverse temperature as bifurcation parameter. To this end, we first employed formal asymptotics with respect to the correlation time of the noise for the steady states. In order to obtain results that are valid beyond the small correlation time regime, we also used a Hermite spectral method in order to solve the time-dependent mean-field Fokker-Planck equation in the longtime limit and thereby find the steady states.

\noindent • With Gabriel Stoltz and Grigorios Pavliotis, we studied the generalized Langevin equation (GLE) in a periodic potential. Generalized Langevin dy-namics are often used in molecular dynamics and nonequi-librium statistical mechanics to model a particle interacting with a heat bath at equilibrium. For a large class of GLEs, a functional central limit theorem holds: the rescaled position process q(t/2) converges, weakly in C([0, T ]; R) as → 0, to a pure diffusion, with an effective diffusion coefficient that depends on the parameters of the memory kernel. In our work, we first proved sharp longtime convergence estimates to the stationary state for the GLE using techniques from H 1 hypocoercivity and hypoelliptic regularization. Based on these estimates, we then proved asymptotic results for the effective diffusion coefficient in three asymptotic regimes of particular interest: short correlation, large friction and low friction. Finally, we employed a recently developed spectral numerical method in order to calculate the effective diffusion coefficient for a wide range of parameters the memory kernel, thereby corroborating our asymptotic results.

\noindent Collaborators. The first work described above was conducted in collaboration with Susana Gomes (Maths, Warwick) and Grigorios Pavliotis. The second was a collaboration with Grigorios Pavliotis and Gabriel Stoltz (Maths, ENPC Inria).

\noindent Originality and impact. The use of colored noise in the interacting particle system studied in the first project presents several difficulties compared to the white noise case: the mean-field Fokker-Planck equation no longer has an explicit gradient structure, there is no longer an explicit expression for its steady states, and the equation is hypoelliptic. Using colored noise also leads to more computationally expensive simulations of the mean-field PDE, because of the extra dimensions corresponding to the noise.

\noindent The main difficulty encountered in the second project was to obtain, based on Villani's hypocoercivity framework, convergence rates with an explicit dependence on the physical parameters of the equation. Since off-the-shelf hypocoercivity results were not sufficiently quantitative to this end, we developed a methodology for obtaining the scalings of the coefficients appearing in the modified inner product and, from there, deriving precise convergence rates. It would be worthwhile to investigate whether our method can be extended to other highdimensional hypocoercive systems, such as atom chains. We also identified an interesting open question related to the GLE: is it possible to obtain L2 resolvent estimates more directly than via the H 1 approach, for example through the direct L2 hypocoercivity framework developed in [5, 6] , or through the more recent approach based on Schur complements developed in [1] ? (See the end of section 4 for the list of references.)

\noindent Dissemination. This work led to two publications, \cite{GPV20} and \cite{PStoV21}, and it also led to the further development of Hermipy. 9 3.3 Derivative-free methods for inverse problems Description of the contribution. Inverse problems are ubiquitous because they formalize the integration of data with mathematical models. In general, they can be formulated in the following form: find an unknown parameter u X from data y Y , given that the two are related by an equation of the form y = G(u) + ,

\noindent where G is the parameter-to-data map and is observational noise whose exact value is unknown but whose statistical properties may be known. In many scientific applications, the forward model is expensive to evaluate and adjoint computations are difficult to employ; in this setting, derivative-free methods which involve a small number of forward model evaluations are a very attractive proposition. During my postdoc at Imperial College London, one of my main research activities was the analysis and development of derivative free methods for Bayesian inverse problems. My contributions in this area are the following:

\noindent • With José Carrillo, we proved sharp convergence estimates in Wasserstein distance for inversion and sampling methods based on the ensemble Kalman filter. To this end we employed techniques from optimal transport in order to study the partial differential equations that describe the evolution of the interacting particle systems in the limit of infinitely many particles. Our estimates show that, at the mean-field level, the convergence rate of ensemble Kalman methods is constant across all linear inverse problems.

\noindent • In later work with Grigorios Pavliotis and Andrew Stuart, we proposed a novel refineable derivative-free method based on a multiscale dynamics for posterior sampling or maximum a posteriori estimation. Using techniques from multiscale and numerical analysis, we proved the convergence of the approximate solution, at both the continuous-time and discrete-time levels, to a preconditioned overdamped Langevin dynamics in an appropriate limit for the parameters of the method. We also demonstrated the efficiency of the method through careful numerical experiments.

\noindent • In a more recent work that will soon be submitted for publication, we demonstrate how ideas from consensus-based optimization could be employed for the purpose of sampling. We propose an efficient sampling method based on consensus and prove its key properties, such as affine invariance and the exponential convergence of the associated mean-field Fokker-Planck equation in simple settings. We also show how, with an appropriate set of parameters, the method can be employed for optimization.

\noindent Collaborators. The first work described above was realized in collaboration with José Carrillo (Maths, Oxford). The second work was carried out in collaboration with Andrew Stuart (Maths, Caltech) and Grigorios Pavliotis, and the third is the result of a collaboration with José Carrillo, Franca Hoffmann (Maths, University of Bonn) and Andrew Stuart.

\noindent Originality and impact. Ensemble Kalman based interacting particle systems have proven empirically successful in the context of Bayesian inverse problems, but few theoretical works have studied their conver-gence properties. In addition, they suffer from the fact that they cannot be systematically refined to sample more precisely from the posterior distribution. The aims of the first two contributions above were respec-tively to advance the state of understanding of ensemble Kalman methods, and to propose an alternative method that overcomes the issue of uncontrolled gradient approximations affecting the Ensemble Kalman sampler. The third contribution was more exploratory; it aimed to determine the viability of consensus-based approaches, which had previously been employed in the context of optimization, for the purpose of sampling.

\noindent Through this work, and in particular through the third study above, we identified promising research avenues that we hope to pursue in the future. For example, we showed empirically that progressively decreasing a temperature parameter, in the style simulated annealing, leads to a much faster convergence of consensus-based optimization and sampling methods, and we believe that this behavior could be explained and studied via rigorous mathematical analysis.

\noindent Dissemination This work led to the paper \cite{CV20}, the submitted preprint \cite{PStuV21}, and the study \cite{CHSV21} which will soon be submitted for publication. It also led to the development of a code imple-menting ensemble Kalman and consensus-based methods for sampling and inversion (Bayesicle), which is still under active development. 10 4 Research program and insertion within the research laboratory I present in this section a research program that I would be very enthusiastic to incorporate, in part or in full, in my future research activity. I will also be very happy to develop, in parallel, new collaborations with members of the Laboratoire de Mathématiques Appliquées de Compiègne (L.M.A.C.) on subjects related to the areas of expertise of the laboratory. I discuss at the end of this section my insertion within L.M.A.C. and the interactions I envisage with members of the laboratory.

\noindent Research program title: Sampling in high dimensions: from mathematical analysis to practical algorithms

\noindent The main aim of this research project is to improve, mathematically analyze and implement efficient sampling methods for inverse problems and computational statistical physics. The project lies at the interface between partial differential equations (PDEs), numerical analysis and computational statistics, and it comprises both theoretical and numerical aspects. On the one hand, I intend to study and improve sampling and optimization methods based on interacting particle systems, particularly in the context of statistical inverse problems. Work of this type will help lay the theoretical foundations of this emerging field and, when combined with changes in computer architectures that facilitate the efficient implementation of interacting particle systems, has the potential to significantly impact applications. On the other hand, I propose to develop efficient variance reduction approaches in computational statistical physics, with an emphasis on nonequilibrium systems and the ambition to accelerate sampling methods in this field, which will open the door to new applications. I very much believe in the cross-fertilization and blending of successful techniques from one subfield to the other. Below, I first present the context motivating this research program and then develop its two parts.

\noindent Context. Many scientific applications require the calculation of expectations with respect to high-dimensional probability distributions. A widely-used approach to this end, known as the Markov chain Monte Carlo (MCMC) method, is to simulate a long trajectory of a stochastic dynamics that admits the target distri-bution as unique invariant measure, and to approximate expectations by time averages over this trajectory. Although the original MCMC method dates back to the 1950s [16] , the development and analysis of sampling methods remains at present an extremely active area of mathematical research, driven by the ever-increasing amount of data available to scientists, a desire to understand high-dimensional problems, and changes in computer architecture. As mentioned in the introduction, we discuss in this research project two mathematical disciplines in which access to robust and efficient numerical methods for high-dimensional sampling is particularly important: computational statistical methods for inverse problems and computational statistical physics. These two application domains present different challenges and constraints.

\noindent • The language of inverse problems provides a mathematical formalization for the assimilation of data into predictive models. Approaching these problems through the Bayesian viewpoint brings consid-erable insight, as it enables to assign probabilities to all possible solutions and thereby to quantify uncertainty [22] . In this setting, the usual method for acquiring information on the true solution is to generate samples from the probability distribution that best describes it given the data -the Bayesian posterior. This distribution is typically high-dimensional and known up to a constant factor, but in many applications it is expensive to evaluate and its derivatives are unavailable or too costly to obtain. Gradient-free sampling methods that require few evaluations of the target distribution are thus highly desirable. An additional difficulty is that the target distribution often exhibits strong anisotropy: the condition number of the Hessian of the target distribution is large in regions of high posterior prob-ability density. In order to appropriately handle this difficulty, a frequent additional requirement for sampling methods in the context of Bayesian inverse problems is that they are invariant under affine changes of coordinates, a property known as affine invariance [11, 10] .

\noindent • Molecular dynamics, also known as computational statistical physics, focuses on the simulation of atomistic systems using Newtonian models, with the primary aim of calculating macroscopic prop-erties of matter. Standard examples of such properties include static (or thermodynamic) quantities such as heat capacities and equations of state relating pressure, density and temperature, as well as dynamic quantities such as transport coefficients (e.g. the mobility, the shear viscosity and the ther-mal conductivity). Molecular dynamics may also be employed in order to gain a better understanding of atomistic systems whose direct observation is too expensive or beyond the current capabilities of experimental physics. In addition to providing valuable insight into the behavior of microscopic sys-tems, these "in silico experiments" can be used for guiding technological developments, particularly in the field of materials science. Other areas where molecular dynamics plays an important role include 11 biology, chemistry and drug discovery. There are several specific difficulties associated with sampling in the context of molecular dynamics. First, the target distribution is often multimodal, leading to metastability issues of many sampling algorithms: most of the time, the iterates are trapped in some region of high probability density -a metastable state -and on rare occasions they hop to another metastable state. This is-sue often leads to large statistical errors of ergodic estimators as it requires to simulate very long trajectories in order to guarantee a good exploration of the configuration space. A second difficulty is that the stochastic dynamics of interest are usually degenerate, with noise acting only on some degrees of freedom, and so dedicated mathematical techniques are required for their analysis. Finally, in nonequilibrium settings, there is generally no explicit expression for the target probability distribution and, consequently, many of the standard methods for variance reduction are not directly applicable.

\noindent 1. Sampling and optimization methods for statistical inverse problems. In recent years, there has been significant activity to develop sampling methods based on interacting particle systems, which can leverage recent advances in parallel computing. Examples of current interest include interacting particle MCMC methodologies [4] , Stein variational gradient descent [14, 15] , the ensemble Kalman sampler [9] and consensus-based sampling \cite{CHSV21}. Being derivative-free and affine invariant, the latter two methods are particularly well-suited for inverse problems. Since they are based on systems of interacting stochas-tic differential equations, they may be studied by means of the nonlocal Fokker-Planck PDEs associated with the systems in the limit of infinitely many particles. This approach of studying interacting particle numerical algorithms from a mean-field viewpoint was first proposed in [19] in an optimization context and has since brought considerable insight. Since both ensemble Kalman and consensus-based sampling were proposed recently, there are many exciting research avenues concerning their analysis as well as possible improvements and extensions. The research I propose to undertake will help to make significant advances in this emerging field.

\noindent • Firstly, I propose to explore several research directions that naturally follow from previous work in \cite{CV20} and \cite{CHSV21}. I will perform a quantitative analysis of the connection between the interact-ing particle systems at the roots of ensemble Kalman and consensus based methods and their mean field limit, which will provide insight into the optimal number of particles to use in such simula-tions. I will also endeavor to derive more general convergence results for the nonlocal Fokker-Planck equations describing these methods in the mean field limit than those previously obtained, which focused predominantly on the Gaussian setting. Next, I intend to study theoretically a variation of consensus-based sampling and optimization where a temperature parameter is progressively decreased, in the style of simulated annealing. This approach is observed in our numerical experiments to signif-icantly accelerate convergence, but still lacks a theoretical footing. On the numerical front, it will be worthwhile to examine whether consensus-based sampling and its optimization variant are competi-tive outside the context of Bayesian inverse problems, e.g. for high-dimensional optimization problems arising in the training of large neural networks.

\noindent • Secondly, I propose to study whether the accuracy of ensemble Kalman and consensus-based sampling methods can be improved. Indeed, a drawback of these approaches is that they generate samples from a probability distribution that only approximates the desired target distribution. Consensus-based sampling, for example, aims at producing samples from the multivariate normal distribution that best approximates, in an appropriate sense, the target density. This can lead to potentially large errors when the target density is very different from Gaussian, making the method ill-suited in these situations. In the context of Bayesian inference, the posterior distribution is generally close to Gaussian provided that sufficiently many data are available (by the Bernstein-von Mises theorem), so this limitation is somewhat mitigated, but large errors can still arise when data are scarce. In order to remedy this issue, a promising approach would be to investigate whether ensemble Kalman and consensus-based methods can be coupled to a Metropolis-Hastings accept/reject step for better accuracy. Alternatively, one could integrate these methods in a composite sampling methodology, such as the one based on parameter-to-data map emulation recently proposed in [2] . Finally, it would be very useful to develop methods for estimating a posteriori the discrepancy between the actual target distribution and its numerical approximation.

\noindent 2. Sampling methods in computational statistical physics. In recent decades, there has been significant activity devoted to improving and analyzing variance reduction methods for sampling algorithms, both for standard Monte Carlo methods and for MCMC methods. Let us mention, for example, recent 12 developments in importance sampling [3, 13] and control variates [17, 18, 20] . In this project, I propose to develop, study and improve variance reduction approaches in molecular dynamics, particularly for the computation of transport coefficients. For concreteness of the discussion, let us consider the Langevin equation, which is a paradigmatic model in molecular dynamics: dqt = M 1pt dt, dpt = qV (qt) dt M 1 pt dt + (cid:112) 21 dWt.

\noindent Here qt Rd and pt Rd are the position and momentum of a particle in Rd, is the friction parameter, is the inverse temperature, V is an external potential and M is the mass matrix. The Langevin dynamics admits the Boltzmann-Gibbs measure µ exp(cid:0)(V (q) + pT M 1p/2)(cid:1) as unique invariant distribution, and so it can be employed for calculating expectations of observables with respect to µ via time averages over a long trajectory. When V has several deep local minima, however, the Langevin dynamics is metastable, which leads to a large asymptotic variance of the associated estimators. Note that the asymptotic variance of ergodic estimators can be expressed in terms of the solution to a Poisson PDE involving the generator of the Markov semigroup corresponding to (4), and that stochastic dynamics of the type (4) are often analyzed through the associated Fokker-Planck and backward Kolmogorov PDEs. The Fokker-Planck equation associated with the dynamics (4), for instance, governs the time evolution of the law of the random variable (qt, pt); it reads t = L, L = (M 1p) • q + qV (q) • p + p • (cid:0)M 1p + 1p(cid:1) .

\noindent Equations of this type play a major role in the research directions presented below.

\noindent • As a first research direction, I propose to investigate whether using a modification of (4) where V is replaced V + (cid:101)V , for some perturbation potential (cid:101)V , can be useful for reducing the variance of ergodic estimators. A reweighting factor would then be incorporated in the expectation in order to ensure that the estimators are asymptotically unbiased. The motivation behind this surprisingly unexplored idea is that, by changing the drift appropriately, it is possible to increase the rate of convergence to equilibrium of the dynamics and reduce metastability issues, which provides scope for variance reduction. I will aim to determine, for example, the optimal choice of (cid:101)V in order to minimize the asymptotic variance for a given observable, or a given class of observables. This approach may prove useful as a general statistical method, with applications beyond molecular dynamics.

\noindent The calculation of transport coefficients is a difficult problem in molecular dynamics: on the one hand, equilibrium approaches, which are based on the fact that transport coefficients can be expressed as time integrals of appropriate correlation functions (these are the Green-Kubo formulas), are plagued by relative statistical errors that diverge to infinity as the integration time increases. On the other hand, non-equilibrium approaches based on calculating the response of the system induced by a small forcing suffer from large relative statistical errors, because the amplitude of the forcing needs to be small in order for the system to remain in the linear response regime. Although most practitioners of molecular dynamics recognize these difficulties, there have been very few attempts to develop bespoke variance reduction techniques. I discuss below two directions towards improving this state of affairs.

\noindent • First, it would be interesting to study whether ideas from importance sampling can be leveraged for calculating transport coefficients more efficiently. More precisely, it may be the case that employing a perturbed dynamics of the type (4) with V replaced by V + (cid:101)V could be useful for the calculation of transport coefficients through Green-Kubo's formula, an idea that received some attention in [7] . Since Green-Kubo's formula can be viewed as an expectation with respect to the law of the process, this change of the dynamics is corrected for by including a Girsanov weight in the expectation. There are several natural mathematical questions related to this idea. First, can we optimally choose (cid:101)V in order to minimize the variance of estimators of transport coefficients? In particular, there is a trade-off between the decrease in the variance of estimators brought about by the faster convergence to equilibrium of the modified dynamics and a possible increase in the variance due to the presence of the reweighting factors. Second, can the transport coefficients be obtained from the modified dynamics without recurring to Girsanov's weights, as these may prove too detrimental to the variance?

\noindent • Second, I propose to study a method for improving the estimators of transport coefficients relying on an external forcing and linear response. The motivating idea here is that there is some freedom in the choice of the external forcing applied to the system, which could be exploited in order to define bet-ter estimators. In particular, forcings that are not physically realizable, called synthetic forcings in [8] , may be employed. A natural question associated with this idea is the following: can we choose 13 the forcing in such a way that the region of validity of the linear response regime is broadened, which would enable to choose a larger forcing in simulations in order to reduce the relative statistical error? Besides these two approaches, which are mid-term goals for this project, I emphasize that there are many other perspectives for more efficiently computing transport coefficients. In essence, many variance reduction techniques for computing averages with respect to probability measures have to be re-thought and extended to cover the case of properties such as transport coefficients, which include some dynamical information.

\noindent • To conclude, I mention a more theoretical research direction that we identified in \cite{PStoV21}: can a longtime convergence estimate to the stationary state be obtained for the generalized Langevin equa-tion (GLE) through the direct L2 hypocoercivity framework developed in [12, 5, 6] , or through the more recent approach based on Schur complements introduced in [1]? The GLE, which was mentioned in section 3.2, is a generalization of the Langevin dynamics (4) which, in the quasi-Markovian setting, can be recast as a Markovian SDE over an extended phase space. Like the Langevin dynamics, it can be employed for sampling from the Boltzmann-Gibbs measure µ, but the study of convergence to equilibrium for the associated Fokker-Planck equation, in this case also a hypoelliptic PDE, is more challenging because of the extra dimensions. The direct L2 hypocoercivity approach is attrac-tive because it makes it easier to quantify convergence rates and, unlike the H 1 approach employed in \cite{PStoV21}, it tends to generalize well to numerical schemes for Fokker-Planck-type equations, such as spectral methods [21] .

\noindent Insertion within the Laboratoire de Mathématiques Appliquées de Compiègne (L.M.A.C.). I am convinced that my strong abilities in fields ranging from analysis and probability to numerical partial differential equations (PDEs) and computational stochastic dynamical systems will enable me to thrive within L.M.A.C., and more specifically within the team on "Problèmes Inverses et Analyse Numérique" (EPIA). My presence will both consolidate the team's position at the forefront of research in inverse problems and numerical analysis, and help it play a leading role in emerging research concerning numerical methods for high-dimensional sampling, particularly in the context of Bayesian inverse problems. Within the EPIA team, I would be very enthusiastic to collaborate with Florian De Vuyst, with whom I could work the development of parallel-in-time methods for time-dependent differential equations that can conserve symplectic properties, and also with Ahmad El Hajj who, being an expert in inverse problems and nonlinear/nonlocal PDEs, would be an ideal collaborator on the part of my research project that concerns the analysis of interacting particle methods for Bayesian inverse problems. Besides the research program above, I would very much enjoy the opportunity to develop collaborations with members of EPIA on other research projects stemming from real-world applications. My skills in mechanical engineering, which I acquired in my master's degree, will prove useful for developing fruitful interactions with more applied laboratories within the university. I will also be enthusiastic to co-supervise PhD candidates in the laboratory, a task for which I feel well-prepared in view of my extensive teaching and programming experience.

\end{document}