import json
from pylatexenc.latexencode import unicode_to_latex

# 加载 GROBID JSON 文件
with open(r"D:\workspace\french_cv\output_dir\mcf_analytical_presentation.json", "r", encoding="utf-8") as f:
    data = json.load(f)

output_lines = []
output_lines.append(r"\documentclass{article}")
output_lines.append(r"\usepackage{amsmath,amssymb}")
output_lines.append(r"\begin{document}")

for block in data:
    if "text" in block:
        # 解码 unicode 转义字符
        decoded = block["text"].encode().decode('unicode_escape')

        # 替换为 LaTeX 命令
        latex_text = unicode_to_latex(decoded)

        # 尝试判断是否是公式段落
        if latex_text.strip().startswith(('∂', '\u2202', 'L', 'd/d', 'Δ', '∇')):
            output_lines.append(r"\[ " + latex_text + r" \]")
        else:
            output_lines.append(r"\noindent " + latex_text + "\n")

output_lines.append(r"\end{document}")

# 写入到输出文件
with open("output.tex", "w", encoding="utf-8") as f:
    f.write("\n".join(output_lines))

print("✅ LaTeX 文件已生成：output.tex")
