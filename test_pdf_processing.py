#!/usr/bin/env python3

import os
import sys
import json

# Add the s2orc-doc2json to the path
sys.path.insert(0, 's2orc-doc2json')

from doc2json.grobid2json.process_pdf import process_pdf_file

def main():
    # Define paths
    # input_pdf = r"D:\workspace\Paper2Code-main\s2orc-doc2json\tests\pdf\A_Linear_Three-Phase_Load_Flow_for_Power_Distribution_Systems (1).pdf"

    # input_pdf = r"D:\workspace\Paper2Code-main\s2orc-doc2json\tests\pdf\Wan et al. - 2025 - Think twice, act once A co-evolution framework of LLM and RL for large-scale decision making.pdf"
    

    input_pdf = r"D:\workspace\french_cv\mcf_analytical_presentation_original.pdf"

    
    temp_dir = "temp_dir"
    output_dir = "output_dir"
    
    # Check if input file exists
    if not os.path.exists(input_pdf):
        print(f"Error: Input file does not exist: {input_pdf}")
        return
    
    print(f"Processing PDF: {input_pdf}")
    print(f"Temp directory: {temp_dir}")
    print(f"Output directory: {output_dir}")
    
    try:
        # First, let's test GROBID directly
        from doc2json.grobid2json.grobid.grobid_client import GrobidClient

        print("Testing GROBID connection...")
        client = GrobidClient()

        # Get paper id as the name of the file
        paper_id = '.'.join(os.path.basename(input_pdf).split('.')[:-1])
        tei_file = os.path.join(temp_dir, f'{paper_id}.tei.xml')

        print(f"Expected TEI file: {tei_file}")
        print(f"Paper ID: {paper_id}")

        # Try to process PDF through GROBID directly
        print("Calling GROBID to process PDF...")
        client.process_pdf(input_pdf, temp_dir, "processFulltextDocument")

        # Check if TEI file was created
        if os.path.exists(tei_file):
            print(f"TEI file created successfully: {tei_file}")
            print(f"TEI file size: {os.path.getsize(tei_file)} bytes")

            # Now try the TEI to JSON conversion directly
            from doc2json.grobid2json.tei_to_json import convert_tei_xml_file_to_s2orc_json

            print("Converting TEI to JSON...")
            paper = convert_tei_xml_file_to_s2orc_json(tei_file)

            # Create output file path
            output_file = os.path.join(output_dir, f'{paper_id}.json')

            # Write to file
            with open(output_file, 'w', encoding='utf-8') as outf:
                json.dump(paper.release_json(), outf, indent=4, sort_keys=False)

            print(f"Successfully processed! Output file: {output_file}")

            # Check if output file was created
            if os.path.exists(output_file):
                print(f"Output file size: {os.path.getsize(output_file)} bytes")

                # Load and show basic info about the JSON
                with open(output_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                print(f"JSON structure keys: {list(data.keys())}")
                if 'title' in data:
                    print(f"Paper title: {data['title']}")
                if 'abstract' in data:
                    print(f"Abstract length: {len(data['abstract'])} characters")
                if 'body_text' in data:
                    print(f"Body text sections: {len(data['body_text'])}")

            else:
                print("Error: Output file was not created")
        else:
            print(f"Error: TEI file was not created by GROBID: {tei_file}")
            print("Checking temp directory contents:")
            if os.path.exists(temp_dir):
                for file in os.listdir(temp_dir):
                    print(f"  - {file}")
            else:
                print("  Temp directory does not exist")

    except Exception as e:
        print(f"Error processing PDF: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
