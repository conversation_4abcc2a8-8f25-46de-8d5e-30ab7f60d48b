This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.12.2)  24 JUN 2025 17:25
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/workspace/french_cv/output_dir/mcf_analytical_presentation_unicode_enhanced
(d:/workspace/french_cv/output_dir/mcf_analytical_presentation_unicode_enhanced.tex
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2024-11-02>
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (c:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (c:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen142
)) (c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count270
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count271
\leftroot@=\count272
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count273
\DOTSCASE@=\count274
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count275
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count276
\dotsspace@=\muskip17
\c@parentequation=\count277
\dspbrk@lvl=\count278
\tag@help=\toks20
\row@=\count279
\column@=\count280
\maxfields@=\count281
\andhelp@=\toks21
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (c:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
) (c:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (c:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (c:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (c:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (c:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (c:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (c:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (c:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (c:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (c:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (c:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (c:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count282
) (c:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count283
) (c:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen153
\Hy@linkcounter=\count284
\Hy@pagecounter=\count285
 (c:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (c:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count286
 (c:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count287
 (c:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen154
 (c:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (c:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count288
\Field@Width=\dimen155
\Fld@charsize=\dimen156
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (c:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count289
\c@Item=\count290
\c@Hfootnote=\count291
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (c:/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (c:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count292
\c@bookmark@seq@number=\count293
 (c:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (c:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip54
) (c:/texlive/2024/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2024/11/30 v24.14 The multilingual framework for pdfLaTeX, LuaLaTeX and XeLaTeX
\babel@savecnt=\count294
\U@D=\dimen157
\l@unhyphenated=\language89
 (c:/texlive/2024/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count295
 (c:/texlive/2024/texmf-dist/tex/generic/babel-french/french.ldf
Language: french 2024-07-25 v3.6c French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language31). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language31). Reported on input line 92.
\FB@stdchar=\count296
Package babel Info: Making : an active character on input line 421.
Package babel Info: Making ; an active character on input line 422.
Package babel Info: Making ! an active character on input line 423.
Package babel Info: Making ? an active character on input line 424.
\FBguill@level=\count297
\FBold@everypar=\toks25
\FB@Mht=\dimen158
\mc@charclass=\count298
\mc@charfam=\count299
\mc@charslot=\count300
\std@mcc=\count301
\dec@mcc=\count302
\FB@parskip=\dimen159
\listindentFB=\dimen160
\descindentFB=\dimen161
\labelindentFB=\dimen162
\labelwidthFB=\dimen163
\leftmarginFB=\dimen164
\parindentFFN=\dimen165
\FBfnindent=\dimen166
)) (c:/texlive/2024/texmf-dist/tex/generic/babel/locale/fr/babel-french.tex
Package babel Info: Importing font and identification data for french
(babel)             from babel-fr.ini. Reported on input line 11.
) (c:/texlive/2024/texmf-dist/tex/latex/carlisle/scalefnt.sty) (c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count303
\l__pdf_internal_box=\box54
)
No file mcf_analytical_presentation_unicode_enhanced.aux.
\openout1 = `mcf_analytical_presentation_unicode_enhanced.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 13.
LaTeX Font Info:    ... okay on input line 13.
(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count304
\scratchdimen=\dimen167
\scratchbox=\box55
\nofMPsegments=\count305
\nofMParguments=\count306
\everyMPshowfont=\toks26
\MPscratchCnt=\count307
\MPscratchDim=\dimen168
\MPnumerator=\count308
\makeMPintoPDFobject=\count309
\everyMPtoPDFconversion=\toks27
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package hyperref Info: Link coloring OFF on input line 13.
\@outlinefile=\write3
\openout3 = `mcf_analytical_presentation_unicode_enhanced.out'.

LaTeX Info: Redefining \degres on input line 13.
LaTeX Info: Redefining \up on input line 13.
LaTeX Font Info:    Trying to load font information for U+msa on input line 16.
 (c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 16.
 (c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-t1.enc}]

[2{c:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]

[3]

[4]
Overfull \hbox (2.02278pt too wide) in paragraph at lines 132--133
[]\T1/cmr/m/n/10 Collaborators. This work was a col-la-bo-ra-tion with Gri-go-rios Pav-lio-tis (Maths,
 []



[5]

[6]
d:/workspace/french_cv/output_dir/mcf_analytical_presentation_unicode_enhanced.tex:152: Misplaced alignment tab character &.
l.152 ...vliotis and Gabriel Stoltz (Maths, ENPC &
                                                   Inria).
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.



[7]

[8]
Overfull \hbox (5.49593pt too wide) in paragraph at lines 198--198
\T1/cmr/bx/n/14.4 men-sions : from ma-the-ma-ti-cal ana-ly-sis to prac-
 []



[9]

[10]

[11]

[12]

[13]

[14] (./mcf_analytical_presentation_unicode_enhanced.aux)
 ***********
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2024-11-02>
 ***********


Package rerunfilecheck Warning: File `mcf_analytical_presentation_unicode_enhanced.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `mcf_analytical_presentation_unicode_enhanced.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  EF6FC4BD4F4DB12D96029B85CE72111F;2707.
 ) 
Here is how much of TeX's memory you used:
 11817 strings out of 473330
 184598 string characters out of 5723878
 596407 words of memory out of 5000000
 34795 multiletter control sequences out of 15000+600000
 568586 words of font info for 68 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,6n,79p,1857b,456s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfbx1440.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1728.pfb>
Output written on mcf_analytical_presentation_unicode_enhanced.pdf (14 pages, 137322 bytes).
PDF statistics:
 118 PDF objects out of 1000 (max. 8388607)
 88 compressed objects within 1 object stream
 25 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

