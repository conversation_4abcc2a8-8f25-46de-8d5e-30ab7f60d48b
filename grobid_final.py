import os
import sys
import json
import re
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# Add the s2orc-doc2json path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 's2orc-doc2json'))

from doc2json.grobid2json.grobid.grobid_client import GrobidClient
from doc2json.grobid2json.process_pdf import process_pdf_file

def clean_math_content(text: str) -> str:
    """Clean and format mathematical content for LaTeX"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Handle common mathematical symbols and expressions
    replacements = {
        '≤': r'\leq', '≥': r'\geq', '≠': r'\neq', '±': r'\pm', '∞': r'\infty',
        '∑': r'\sum', '∏': r'\prod', '∫': r'\int', '∂': r'\partial', '∇': r'\nabla',
        '√': r'\sqrt', 'α': r'\alpha', 'β': r'\beta', 'γ': r'\gamma', 'δ': r'\delta',
        'ε': r'\epsilon', 'θ': r'\theta', 'λ': r'\lambda', 'μ': r'\mu', 'π': r'\pi',
        'σ': r'\sigma', 'τ': r'\tau', 'φ': r'\phi', 'ψ': r'\psi', 'ω': r'\omega',
        'Γ': r'\Gamma', 'Δ': r'\Delta', 'Θ': r'\Theta', 'Λ': r'\Lambda',
        'Π': r'\Pi', 'Σ': r'\Sigma', 'Φ': r'\Phi', 'Ψ': r'\Psi', 'Ω': r'\Omega',
        '→': r'\to', '←': r'\leftarrow', '↔': r'\leftrightarrow', '⇒': r'\Rightarrow',
        '⇐': r'\Leftarrow', '⇔': r'\Leftrightarrow', '∈': r'\in', '∉': r'\notin',
        '⊂': r'\subset', '⊃': r'\supset', '⊆': r'\subseteq', '⊇': r'\supseteq',
        '∪': r'\cup', '∩': r'\cap', '∅': r'\emptyset', '×': r'\times', '·': r'\cdot',
    }
    
    for unicode_char, latex_cmd in replacements.items():
        text = text.replace(unicode_char, latex_cmd)
    
    return text

def main():
    """Main function to process the PDF"""
    pdf_path = 'mcf_analytical_presentation.pdf'
    output_path = 'mcf_analytical_presentation_final.tex'
    
    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        return
    
    print(f"Processing PDF: {pdf_path}")
    
    # Create temporary directories
    temp_dir = "temp_dir"
    output_dir = "output_dir"
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Process PDF with GROBID (reuse existing if available)
        paper_id = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
        json_file = os.path.join(output_dir, f'{paper_id}.json')
        
        if not os.path.exists(json_file):
            print("Processing PDF with GROBID...")
            json_file = process_pdf_file(pdf_path, temp_dir, output_dir)
        else:
            print("Using existing GROBID output...")
        
        # Load the JSON output
        with open(json_file, 'r', encoding='utf-8') as f:
            paper_data = json.load(f)
        
        # Check for body_text in pdf_parse section
        body_text = []
        if 'pdf_parse' in paper_data and 'body_text' in paper_data['pdf_parse']:
            body_text = paper_data['pdf_parse']['body_text']
        elif 'body_text' in paper_data:
            body_text = paper_data['body_text']

        print(f"Found {len(body_text)} body text sections")
        
        # Start building LaTeX document
        latex_content = []
        
        # Document class and packages
        latex_content.extend([
            "\\documentclass[11pt]{article}",
            "\\usepackage[utf8]{inputenc}",
            "\\usepackage[T1]{fontenc}",
            "\\usepackage{amsmath}",
            "\\usepackage{amsfonts}",
            "\\usepackage{amssymb}",
            "\\usepackage{mathtools}",
            "\\usepackage{geometry}",
            "\\usepackage{graphicx}",
            "\\usepackage{hyperref}",
            "\\usepackage{cite}",
            "\\usepackage[french,english]{babel}",
            "",
            "\\geometry{margin=1in}",
            "",
            "\\begin{document}",
            ""
        ])
        
        # Title and metadata
        if 'title' in paper_data and paper_data['title']:
            title = clean_math_content(paper_data['title'])
            latex_content.append(f"\\title{{{title}}}")
        
        if 'authors' in paper_data and paper_data['authors']:
            authors = []
            for author in paper_data['authors']:
                if 'first' in author and 'last' in author:
                    authors.append(f"{author['first']} {author['last']}")
                elif 'name' in author:
                    authors.append(author['name'])
            if authors:
                author_string = ' \\and '.join(authors)
                latex_content.append(f"\\author{{{author_string}}}")
        
        latex_content.extend([
            "\\maketitle",
            ""
        ])
        
        # Abstract
        if 'abstract' in paper_data and paper_data['abstract']:
            latex_content.append("\\begin{abstract}")
            if isinstance(paper_data['abstract'], str):
                abstract_text = clean_math_content(paper_data['abstract'])
                latex_content.append(abstract_text)
            elif isinstance(paper_data['abstract'], list):
                for abs_section in paper_data['abstract']:
                    if isinstance(abs_section, dict) and 'text' in abs_section:
                        abstract_text = clean_math_content(abs_section['text'])
                        latex_content.append(abstract_text)
                    elif isinstance(abs_section, str):
                        abstract_text = clean_math_content(abs_section)
                        latex_content.append(abstract_text)
            latex_content.extend(["\\end{abstract}", ""])
        
        # Process body text sections
        if body_text:
            current_section = None
            equation_count = 0

            for section in body_text:
                # Handle section headers
                if 'section' in section and section['section'] != current_section:
                    current_section = section['section']
                    section_title = clean_math_content(section['section'])
                    if section_title and section_title.strip() and len(section_title.strip()) > 2:
                        # Determine section level
                        if 'sec_num' in section and section['sec_num']:
                            sec_num = str(section['sec_num'])
                            if '.' in sec_num:
                                latex_content.append(f"\\subsection{{{section_title}}}")
                            else:
                                latex_content.append(f"\\section{{{section_title}}}")
                        else:
                            latex_content.append(f"\\section{{{section_title}}}")
                        latex_content.append("")
                
                if 'text' in section:
                    section_text = clean_math_content(section['text'])
                    
                    # Handle equations marked as "EQUATION" in the text
                    if section_text.strip() == "EQUATION":
                        if 'eq_spans' in section and section['eq_spans']:
                            for eq_span in section['eq_spans']:
                                if 'raw_str' in eq_span:
                                    eq_content = clean_math_content(eq_span['raw_str'])
                                    equation_count += 1
                                    if 'eq_num' in eq_span and eq_span['eq_num']:
                                        eq_label = eq_span['eq_num'].strip('()')
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            f"\\label{{eq:{eq_label}}}",
                                            "\\end{equation}",
                                            ""
                                        ])
                                    else:
                                        latex_content.extend([
                                            "\\begin{equation}",
                                            eq_content,
                                            "\\end{equation}",
                                            ""
                                        ])
                        continue
                    
                    # Skip empty or very short text
                    if section_text.strip() and len(section_text.strip()) > 5:
                        latex_content.append(section_text)
                        latex_content.append("")
        
        # End document
        latex_content.append("\\end{document}")
        
        # Write to output file
        final_latex = "\n".join(latex_content)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_latex)
        
        print(f"LaTeX file created: {output_path}")
        
        # Display statistics
        lines = len(latex_content)
        equations = final_latex.count('\\begin{equation}')
        sections = final_latex.count('\\section{') + final_latex.count('\\subsection{')
        
        print(f"\nDocument statistics:")
        print(f"- Total lines: {lines}")
        print(f"- Sections: {sections}")
        print(f"- Display equations: {equations}")
        
        return output_path
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
