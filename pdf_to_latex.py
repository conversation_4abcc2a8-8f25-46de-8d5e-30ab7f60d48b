import os
import sys
import json
import re
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# Add the s2orc-doc2json path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 's2orc-doc2json'))

from doc2json.grobid2json.grobid.grobid_client import GrobidClient
from doc2json.grobid2json.process_pdf import process_pdf_file

def clean_math_content(text: str) -> str:
    """Clean and format mathematical content for LaTeX"""
    if not text:
        return ""

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())

    # Handle common mathematical symbols and expressions
    # Replace Unicode mathematical symbols with LaTeX equivalents
    replacements = {
        '≤': r'\leq',
        '≥': r'\geq',
        '≠': r'\neq',
        '±': r'\pm',
        '∞': r'\infty',
        '∑': r'\sum',
        '∏': r'\prod',
        '∫': r'\int',
        '∂': r'\partial',
        '∇': r'\nabla',
        '√': r'\sqrt',
        'α': r'\alpha',
        'β': r'\beta',
        'γ': r'\gamma',
        'δ': r'\delta',
        'ε': r'\epsilon',
        'θ': r'\theta',
        'λ': r'\lambda',
        'μ': r'\mu',
        'π': r'\pi',
        'σ': r'\sigma',
        'τ': r'\tau',
        'φ': r'\phi',
        'ψ': r'\psi',
        'ω': r'\omega',
        'Γ': r'\Gamma',
        'Δ': r'\Delta',
        'Θ': r'\Theta',
        'Λ': r'\Lambda',
        'Π': r'\Pi',
        'Σ': r'\Sigma',
        'Φ': r'\Phi',
        'Ψ': r'\Psi',
        'Ω': r'\Omega',
    }

    for unicode_char, latex_cmd in replacements.items():
        text = text.replace(unicode_char, latex_cmd)

    return text

def extract_formulas_from_tei(soup: BeautifulSoup) -> List[Dict]:
    """Extract mathematical formulas from TEI XML"""
    formulas = []

    # Find all formula elements
    formula_elements = soup.find_all('formula')

    for formula in formula_elements:
        formula_data = {
            'id': formula.get('xml:id', ''),
            'type': formula.get('type', 'inline'),
            'content': '',
            'latex': ''
        }

        # Try to get LaTeX content
        latex_elem = formula.find('latex')
        if latex_elem:
            formula_data['latex'] = latex_elem.get_text()

        # Get text content
        formula_data['content'] = clean_math_content(formula.get_text())

        formulas.append(formula_data)

    return formulas

def process_section_content(section_soup, formulas: List[Dict]) -> str:
    """Process section content and integrate formulas"""
    content = ""

    # Get section title
    head = section_soup.find('head')
    if head:
        title = clean_math_content(head.get_text())
        content += f"\\section{{{title}}}\n\n"

    # Process paragraphs
    paragraphs = section_soup.find_all('p')
    for p in paragraphs:
        para_text = clean_math_content(p.get_text())

        # Look for formula references and replace with actual formulas
        for formula in formulas:
            if formula['id'] and formula['id'] in para_text:
                if formula['latex']:
                    if formula['type'] == 'display':
                        formula_latex = f"\\begin{{equation}}\n{formula['latex']}\n\\end{{equation}}"
                    else:
                        formula_latex = f"${formula['latex']}$"
                else:
                    # Fallback to content if no LaTeX available
                    if formula['type'] == 'display':
                        formula_latex = f"\\begin{{equation}}\n{formula['content']}\n\\end{{equation}}"
                    else:
                        formula_latex = f"${formula['content']}$"

                para_text = para_text.replace(formula['id'], formula_latex)

        content += f"{para_text}\n\n"

    return content

def grobid_pdf_to_latex(pdf_path: str, output_path: str) -> str:
    """Convert PDF to LaTeX using GROBID with proper math handling"""

    print(f"Processing PDF: {pdf_path}")

    # Create temporary directories
    temp_dir = "temp_dir"
    output_dir = "output_dir"
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    try:
        # Process PDF with GROBID
        print("Processing PDF with GROBID...")
        json_file = process_pdf_file(pdf_path, temp_dir, output_dir)

        # Also get the TEI XML file for formula extraction
        paper_id = '.'.join(os.path.basename(pdf_path).split('.')[:-1])
        tei_file = os.path.join(temp_dir, f'{paper_id}.tei.xml')

        # Parse TEI XML for mathematical content
        formulas = []
        if os.path.exists(tei_file):
            print("Extracting mathematical formulas from TEI XML...")
            with open(tei_file, 'r', encoding='utf-8') as f:
                tei_content = f.read()
            soup = BeautifulSoup(tei_content, 'xml')
            formulas = extract_formulas_from_tei(soup)
            print(f"Found {len(formulas)} mathematical formulas")

        # Load the JSON output
        with open(json_file, 'r', encoding='utf-8') as f:
            paper_data = json.load(f)

        # Start building LaTeX document
        latex_content = []

        # Document class and packages
        latex_content.extend([
            "\\documentclass[11pt]{article}",
            "\\usepackage[utf8]{inputenc}",
            "\\usepackage[T1]{fontenc}",
            "\\usepackage{amsmath}",
            "\\usepackage{amsfonts}",
            "\\usepackage{amssymb}",
            "\\usepackage{mathtools}",
            "\\usepackage{geometry}",
            "\\usepackage{graphicx}",
            "\\usepackage{hyperref}",
            "\\usepackage{cite}",
            "",
            "\\geometry{margin=1in}",
            "",
            "\\begin{document}",
            ""
        ])

        # Title and metadata
        if 'title' in paper_data:
            title = clean_math_content(paper_data['title'])
            latex_content.append(f"\\title{{{title}}}")

        if 'authors' in paper_data and paper_data['authors']:
            authors = []
            for author in paper_data['authors']:
                if 'first' in author and 'last' in author:
                    authors.append(f"{author['first']} {author['last']}")
                elif 'name' in author:
                    authors.append(author['name'])
            if authors:
                author_string = ' \\and '.join(authors)
                latex_content.append(f"\\author{{{author_string}}}")

        latex_content.extend([
            "\\maketitle",
            ""
        ])

        # Abstract
        if 'abstract' in paper_data and paper_data['abstract']:
            latex_content.append("\\begin{abstract}")
            for abs_section in paper_data['abstract']:
                if 'text' in abs_section:
                    abstract_text = clean_math_content(abs_section['text'])
                    latex_content.append(abstract_text)
            latex_content.extend(["\\end{abstract}", ""])

        # Process body text sections
        if 'body_text' in paper_data and paper_data['body_text']:
            current_section = None
            for section in paper_data['body_text']:
                # Handle section headers
                if 'section' in section and section['section'] != current_section:
                    current_section = section['section']
                    section_title = clean_math_content(section['section'])
                    if section_title and section_title.strip():
                        # Determine section level based on section number
                        if 'sec_num' in section and section['sec_num']:
                            sec_num = str(section['sec_num'])
                            if '.' in sec_num:
                                latex_content.append(f"\\subsection{{{section_title}}}")
                            else:
                                latex_content.append(f"\\section{{{section_title}}}")
                        else:
                            latex_content.append(f"\\section{{{section_title}}}")
                        latex_content.append("")

                if 'text' in section:
                    section_text = clean_math_content(section['text'])

                    # Handle equations marked as "EQUATION" in the text
                    if section_text.strip() == "EQUATION":
                        # Look for corresponding equation in eq_spans
                        if 'eq_spans' in section and section['eq_spans']:
                            for eq_span in section['eq_spans']:
                                if 'raw_str' in eq_span:
                                    eq_content = clean_math_content(eq_span['raw_str'])
                                    if 'eq_num' in eq_span and eq_span['eq_num']:
                                        latex_content.append(f"\\begin{{equation}}")
                                        latex_content.append(f"{eq_content}")
                                        latex_content.append(f"\\label{{eq:{eq_span['eq_num'].strip('()')}}}")
                                        latex_content.append(f"\\end{{equation}}")
                                    else:
                                        latex_content.append(f"\\begin{{equation}}")
                                        latex_content.append(f"{eq_content}")
                                        latex_content.append(f"\\end{{equation}}")
                                    latex_content.append("")
                        continue

                    # Look for and replace formula references
                    for formula in formulas:
                        if formula['id'] and formula['id'] in section_text:
                            if formula['latex']:
                                if formula['type'] == 'display':
                                    formula_latex = f"\\begin{{equation}}\n{formula['latex']}\n\\end{{equation}}"
                                else:
                                    formula_latex = f"${formula['latex']}$"
                            else:
                                if formula['type'] == 'display':
                                    formula_latex = f"\\begin{{equation}}\n{formula['content']}\n\\end{{equation}}"
                                else:
                                    formula_latex = f"${formula['content']}$"

                            section_text = section_text.replace(formula['id'], formula_latex)

                    # Skip empty or very short text
                    if section_text.strip() and len(section_text.strip()) > 3:
                        latex_content.append(section_text)
                        latex_content.append("")

        # Add bibliography if available
        if 'bib_entries' in paper_data and paper_data['bib_entries']:
            latex_content.extend([
                "\\begin{thebibliography}{99}",
                ""
            ])

            for bib_id, bib_entry in paper_data['bib_entries'].items():
                bib_text = ""
                if 'title' in bib_entry:
                    bib_text += f"\\textit{{{clean_math_content(bib_entry['title'])}}}"
                if 'authors' in bib_entry and bib_entry['authors']:
                    authors = []
                    for author in bib_entry['authors']:
                        if 'first' in author and 'last' in author:
                            authors.append(f"{author['first']} {author['last']}")
                        elif 'name' in author:
                            authors.append(author['name'])
                    if authors:
                        bib_text = f"{', '.join(authors)}. {bib_text}"

                if 'venue' in bib_entry:
                    bib_text += f". {clean_math_content(bib_entry['venue'])}"
                if 'year' in bib_entry:
                    bib_text += f" ({bib_entry['year']})"

                latex_content.append(f"\\bibitem{{{bib_id}}} {bib_text}")

            latex_content.extend([
                "",
                "\\end{thebibliography}",
                ""
            ])

        # End document
        latex_content.append("\\end{document}")

        # Write to output file
        final_latex = "\n".join(latex_content)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_latex)

        print(f"LaTeX file created: {output_path}")
        print(f"Found and processed {len(formulas)} mathematical formulas")

        return output_path

    except Exception as e:
        print(f"Error processing PDF with GROBID: {e}")
        raise

def main():
    """Main function to process the PDF"""
    pdf_path = 'mcf_analytical_presentation.pdf'
    output_path = 'mcf_analytical_presentation_grobid.tex'

    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        return

    try:
        result_path = grobid_pdf_to_latex(pdf_path, output_path)
        print(f"\nConversion completed successfully!")
        print(f"Output file: {result_path}")

        # Display some statistics
        with open(result_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = len(content.split('\n'))
            equations = content.count('\\begin{equation}')
            inline_math = content.count('$') // 2  # Divide by 2 since each inline math has 2 $

        print(f"\nDocument statistics:")
        print(f"- Total lines: {lines}")
        print(f"- Display equations: {equations}")
        print(f"- Inline math expressions: {inline_math}")

    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
