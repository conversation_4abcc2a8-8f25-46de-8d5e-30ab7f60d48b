#!/usr/bin/env python3
"""
JSON to LaTeX Converter
Converts JSON files (especially from GROBID PDF parsing) to LaTeX format.
Handles Unicode mathematical symbols and document structure.
"""

import json
import re
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

class JSONToLaTeXConverter:
    def __init__(self):
        # Unicode to LaTeX mapping for mathematical symbols
        self.unicode_to_latex = {
            # Greek letters
            '\u03b1': r'\alpha',
            '\u03b2': r'\beta', 
            '\u03b3': r'\gamma',
            '\u03b4': r'\delta',
            '\u03b5': r'\epsilon',
            '\u03b6': r'\zeta',
            '\u03b7': r'\eta',
            '\u03b8': r'\theta',
            '\u03b9': r'\iota',
            '\u03ba': r'\kappa',
            '\u03bb': r'\lambda',
            '\u03bc': r'\mu',
            '\u03bd': r'\nu',
            '\u03be': r'\xi',
            '\u03bf': r'o',
            '\u03c0': r'\pi',
            '\u03c1': r'\rho',
            '\u03c2': r'\varsigma',
            '\u03c3': r'\sigma',
            '\u03c4': r'\tau',
            '\u03c5': r'\upsilon',
            '\u03c6': r'\phi',
            '\u03c7': r'\chi',
            '\u03c8': r'\psi',
            '\u03c9': r'\omega',
            
            # Capital Greek letters
            '\u0391': r'A',
            '\u0392': r'B',
            '\u0393': r'\Gamma',
            '\u0394': r'\Delta',
            '\u0395': r'E',
            '\u0396': r'Z',
            '\u0397': r'H',
            '\u0398': r'\Theta',
            '\u0399': r'I',
            '\u039a': r'K',
            '\u039b': r'\Lambda',
            '\u039c': r'M',
            '\u039d': r'N',
            '\u039e': r'\Xi',
            '\u039f': r'O',
            '\u03a0': r'\Pi',
            '\u03a1': r'P',
            '\u03a3': r'\Sigma',
            '\u03a4': r'T',
            '\u03a5': r'\Upsilon',
            '\u03a6': r'\Phi',
            '\u03a7': r'X',
            '\u03a8': r'\Psi',
            '\u03a9': r'\Omega',
            
            # Mathematical operators
            '\u2202': r'\partial',
            '\u2207': r'\nabla',
            '\u2212': r'-',  # minus sign
            '\u2213': r'\mp',
            '\u00b1': r'\pm',
            '\u00d7': r'\times',
            '\u00f7': r'\div',
            '\u2022': r'• ',  # bullet point for lists
            '\u2219': r'• ',
            '\u2217': r'• ',
            '\u2260': r'\neq',
            '\u2264': r'\leq',
            '\u2265': r'\geq',
            '\u226a': r'\ll',
            '\u226b': r'\gg',
            '\u2248': r'\approx',
            '\u2261': r'\equiv',
            '\u221d': r'\propto',
            '\u221e': r'\infty',
            '\u2211': r'\sum',
            '\u220f': r'\prod',
            '\u222b': r'\int',
            '\u2208': r'\in',
            '\u2209': r'\notin',
            '\u2282': r'\subset',
            '\u2283': r'\supset',
            '\u2286': r'\subseteq',
            '\u2287': r'\supseteq',
            '\u2229': r'\cap',
            '\u222a': r'\cup',
            '\u2205': r'\emptyset',
            '\u2200': r'\forall',
            '\u2203': r'\exists',
            '\u2192': r'\rightarrow',
            '\u2190': r'\leftarrow',
            '\u2194': r'\leftrightarrow',
            '\u21d2': r'\Rightarrow',
            '\u21d0': r'\Leftarrow',
            '\u21d4': r'\Leftrightarrow',
            
            # Superscripts and subscripts (common ones)
            '\u2070': r'^{0}',
            '\u00b9': r'^{1}',
            '\u00b2': r'^{2}',
            '\u00b3': r'^{3}',
            '\u2074': r'^{4}',
            '\u2075': r'^{5}',
            '\u2076': r'^{6}',
            '\u2077': r'^{7}',
            '\u2078': r'^{8}',
            '\u2079': r'^{9}',
            '\u207a': r'^{+}',
            '\u207b': r'^{-}',
            '\u207c': r'^{=}',
            '\u207d': r'^{(}',
            '\u207e': r'^{)}',
            
            # Special characters
            '\u2026': r'\ldots',
            '\u00a0': ' ',  # non-breaking space
            '\u2009': r'\,',  # thin space
            '\u200a': r'\,',  # hair space
        }
        
        # Patterns for superscript/subscript detection
        self.superscript_pattern = re.compile(r'([a-zA-Z0-9\}])\s*(\u207b?\u00b9|\u207b?\u00b2|\u207b?\u00b3|\u207b?\u2074|\u207b?\u2075|\u207b?\u2076|\u207b?\u2077|\u207b?\u2078|\u207b?\u2079|\u207b?\u2070)')
        
    def convert_unicode_to_latex(self, text: str) -> str:
        """Convert Unicode mathematical symbols to LaTeX commands."""
        # Handle superscript -1 specifically (common in mathematical notation)
        text = re.sub(r'(\w+)\s*\u207b\u00b9', r'\1^{-1}', text)
        text = re.sub(r'(\w+)\s*\u22121', r'\1^{-1}', text)  # Alternative minus-one representation
        
        # Replace Unicode characters with LaTeX equivalents
        for unicode_char, latex_cmd in self.unicode_to_latex.items():
            text = text.replace(unicode_char, latex_cmd)
        
        # Handle remaining superscripts
        text = self.superscript_pattern.sub(r'\1^{\2}', text)
        
        return text
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text for LaTeX."""
        if not text:
            return ""
        
        # Convert Unicode to LaTeX
        text = self.convert_unicode_to_latex(text)
        
        # Fix common spacing issues
        text = re.sub(r'\s+', ' ', text)  # Multiple spaces to single space
        text = text.strip()
        
        # Escape special LaTeX characters (but not math mode characters)
        # Be careful not to escape already converted LaTeX commands
        latex_chars = ['&', '%', '$', '#', '_', '{', '}']
        for char in latex_chars:
            if char not in ['_', '{', '}']:  # Don't escape these in math contexts
                text = text.replace(char, '\\' + char)
        
        return text
    
    def format_equation(self, text: str, eq_num: Optional[str] = None) -> str:
        """Format text as a LaTeX equation."""
        clean_text = self.convert_unicode_to_latex(text)
        
        if eq_num:
            return f"\\begin{{equation}}\n{clean_text}\n\\label{{eq:{eq_num.strip('()')}}}\n\\end{{equation}}"
        else:
            return f"\\begin{{equation}}\n{clean_text}\n\\end{{equation}}"
    
    def process_body_text(self, body_text: List[Dict[str, Any]]) -> str:
        """Process the body text from JSON and convert to LaTeX."""
        latex_content = []
        current_section = None

        for item in body_text:
            text = item.get('text', '')
            section = item.get('section', '')
            sec_num = item.get('sec_num', '')
            eq_spans = item.get('eq_spans', [])

            # Handle section changes
            if section and section != current_section:
                current_section = section
                if sec_num:
                    latex_content.append(f"\\section{{{self.clean_text(section)}}}")
                else:
                    latex_content.append(f"\\section*{{{self.clean_text(section)}}}")
                latex_content.append("")

            # Handle equations
            if eq_spans:
                for eq_span in eq_spans:
                    eq_text = eq_span.get('raw_str', text)
                    eq_num = eq_span.get('eq_num', '')
                    latex_content.append(self.format_equation(eq_text, eq_num))
                    latex_content.append("")
            elif text.strip() == "EQUATION":
                # Skip placeholder equation markers
                continue
            else:
                # Regular text
                clean_text = self.clean_text(text)
                if clean_text:
                    latex_content.append(clean_text)
                    latex_content.append("")

        return "\n".join(latex_content)
    
    def convert_json_to_latex(self, json_data: Dict[str, Any], 
                            include_metadata: bool = True,
                            document_class: str = "article") -> str:
        """Convert JSON data to complete LaTeX document."""
        latex_lines = []
        
        # Document class and packages
        latex_lines.extend([
            f"\\documentclass{{{document_class}}}",
            "\\usepackage[utf8]{inputenc}",
            "\\usepackage{amsmath}",
            "\\usepackage{amssymb}",
            "\\usepackage{amsfonts}",
            "\\usepackage{mathtools}",
            "\\usepackage[margin=1in]{geometry}",
            "\\usepackage{hyperref}",
            "",
            "\\begin{document}",
            ""
        ])
        
        # Title and metadata
        if include_metadata:
            title = json_data.get('title', 'Converted Document')
            if title:
                latex_lines.extend([
                    f"\\title{{{self.clean_text(title)}}}",
                    ""
                ])
            
            # Authors
            authors = json_data.get('authors', [])
            if authors:
                author_names = []
                for author in authors:
                    first = author.get('first', '')
                    last = author.get('last', '')
                    if first and last:
                        author_names.append(f"{first} {last}")
                    elif last:
                        author_names.append(last)
                
                if author_names:
                    latex_lines.extend([
                        f"\\author{{{', '.join(author_names)}}}",
                        ""
                    ])
            
            latex_lines.extend([
                "\\maketitle",
                ""
            ])
        
        # Abstract
        pdf_parse = json_data.get('pdf_parse', {})
        abstract = pdf_parse.get('abstract', [])
        if abstract:
            latex_lines.append("\\begin{abstract}")
            for abs_item in abstract:
                abs_text = self.clean_text(abs_item.get('text', ''))
                if abs_text:
                    latex_lines.append(abs_text)
            latex_lines.extend(["\\end{abstract}", ""])
        
        # Body text
        body_text = pdf_parse.get('body_text', [])
        if body_text:
            body_latex = self.process_body_text(body_text)
            latex_lines.append(body_latex)
        
        # End document
        latex_lines.append("\\end{document}")
        
        return "\n".join(latex_lines)
    
    def convert_file(self, input_file: str, output_file: str = None, 
                    include_metadata: bool = True, document_class: str = "article"):
        """Convert a JSON file to LaTeX."""
        input_path = Path(input_file)
        
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Determine output file name
        if output_file is None:
            output_file = input_path.with_suffix('.tex')
        
        # Load JSON data
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON file: {e}")
        
        # Convert to LaTeX
        latex_content = self.convert_json_to_latex(
            json_data, 
            include_metadata=include_metadata,
            document_class=document_class
        )
        
        # Write output
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        
        print(f"Converted {input_file} to {output_file}")
        return output_file

def main():
    parser = argparse.ArgumentParser(
        description="Convert JSON files (from GROBID) to LaTeX format"
    )
    parser.add_argument("input", help="Input JSON file")
    parser.add_argument("-o", "--output", help="Output LaTeX file (default: input.tex)")
    parser.add_argument("--no-metadata", action="store_true", 
                       help="Skip title, authors, and other metadata")
    parser.add_argument("--document-class", default="article",
                       help="LaTeX document class (default: article)")
    
    args = parser.parse_args()
    
    try:
        converter = JSONToLaTeXConverter()
        converter.convert_file(
            args.input,
            args.output,
            include_metadata=not args.no_metadata,
            document_class=args.document_class
        )
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
