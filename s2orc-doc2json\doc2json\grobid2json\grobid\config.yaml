grobid:
  # NOTE: change these values to absolute paths when running on production
  grobidHome: "grobid-home"
  
  # how to load the models, 
  # false -> models are loaded when needed (default), avoiding puting in memory useless models
  # true -> all the models are loaded into memory at the server statup, slow the start of the services and models not  
  # used will take some memory
  modelPreload: true

server:
    type: custom
    applicationConnectors:
    - type: http
      port: 8070
    adminConnectors:
    - type: http
      port: 8071
    registerDefaultExceptionMappers: false


logging:
  level: WARN
  loggers:
    org.apache.pdfbox.pdmodel.font.PDSimpleFont: "OFF"
  appenders:
    - type: console
      threshold: ALL
      timeZone: UTC
#    - type: file
#      currentLogFilename: logs/grobid-service.log
#      threshold: ALL
#      archive: true
#      archivedLogFilenamePattern: logs/grobid-service-%d.log
#      archivedFileCount: 5
#      timeZone: UTC
