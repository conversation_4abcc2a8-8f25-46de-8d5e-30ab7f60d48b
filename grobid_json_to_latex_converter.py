import json
import re
import os
from typing import Dict, List, Any, Optional

def process_unicode_text(text: str) -> str:
    """
    Convert unicode characters and escape sequences to LaTeX equivalents
    """
    if not text:
        return ""
    
    # Dictionary for common unicode to LaTeX conversions
    unicode_to_latex_map = {
        # Greek letters
        '\u03b1': r'\alpha',
        '\u03b2': r'\beta', 
        '\u03b3': r'\gamma',
        '\u03b4': r'\delta',
        '\u03b5': r'\varepsilon',
        '\u03b6': r'\zeta',
        '\u03b7': r'\eta',
        '\u03b8': r'\theta',
        '\u03b9': r'\iota',
        '\u03ba': r'\kappa',
        '\u03bb': r'\lambda',
        '\u03bc': r'\mu',
        '\u03bd': r'\nu',
        '\u03be': r'\xi',
        '\u03bf': r'o',
        '\u03c0': r'\pi',
        '\u03c1': r'\rho',
        '\u03c3': r'\sigma',
        '\u03c4': r'\tau',
        '\u03c5': r'\upsilon',
        '\u03c6': r'\phi',
        '\u03c7': r'\chi',
        '\u03c8': r'\psi',
        '\u03c9': r'\omega',
        
        # Capital Greek letters
        '\u0391': r'A',
        '\u0392': r'B',
        '\u0393': r'\Gamma',
        '\u0394': r'\Delta',
        '\u0395': r'E',
        '\u0396': r'Z',
        '\u0397': r'H',
        '\u0398': r'\Theta',
        '\u0399': r'I',
        '\u039a': r'K',
        '\u039b': r'\Lambda',
        '\u039c': r'M',
        '\u039d': r'N',
        '\u039e': r'\Xi',
        '\u039f': r'O',
        '\u03a0': r'\Pi',
        '\u03a1': r'P',
        '\u03a3': r'\Sigma',
        '\u03a4': r'T',
        '\u03a5': r'\Upsilon',
        '\u03a6': r'\Phi',
        '\u03a7': r'X',
        '\u03a8': r'\Psi',
        '\u03a9': r'\Omega',
        
        # Mathematical symbols
        '\u2212': r'-',           # minus sign
        '\u2207': r'\nabla',      # nabla
        '\u221e': r'\infty',      # infinity
        '\u221d': r'\propto',     # proportional to
        '\u2208': r'\in',         # element of
        '\u2209': r'\notin',      # not element of
        '\u2211': r'\sum',        # summation
        '\u222b': r'\int',        # integral
        '\u2265': r'\geq',        # greater than or equal
        '\u2264': r'\leq',        # less than or equal
        '\u2260': r'\neq',        # not equal
        '\u2261': r'\equiv',      # equivalent
        '\u2245': r'\cong',       # approximately equal
        '\u2248': r'\approx',     # approximately
        '\u00b1': r'\pm',         # plus minus
        '\u00d7': r'\times',      # multiplication
        '\u00f7': r'\div',        # division
        '\u221a': r'\sqrt',       # square root
        '\u2202': r'\partial',    # partial derivative
        '\u2192': r'\rightarrow', # right arrow
        '\u2190': r'\leftarrow',  # left arrow
        '\u2194': r'\leftrightarrow', # bidirectional arrow
        '\u21d2': r'\Rightarrow', # double right arrow
        '\u21d0': r'\Leftarrow',  # double left arrow
        '\u21d4': r'\Leftrightarrow', # double bidirectional arrow
        '\u2200': r'\forall',     # for all
        '\u2203': r'\exists',     # there exists
        '\u2205': r'\emptyset',   # empty set
        '\u222a': r'\cup',        # union
        '\u2229': r'\cap',        # intersection
        '\u2286': r'\subseteq',   # subset or equal
        '\u2282': r'\subset',     # proper subset
        '\u2288': r'\not\subseteq', # not subset
        '\u2284': r'\not\subset', # not proper subset
        '\u2713': r'\checkmark',  # checkmark
        '\u2717': r'\times',      # cross mark
        
        # Superscripts
        '\u2070': r'^0',
        '\u00b9': r'^1',
        '\u00b2': r'^2',
        '\u00b3': r'^3',
        '\u2074': r'^4',
        '\u2075': r'^5',
        '\u2076': r'^6',
        '\u2077': r'^7',
        '\u2078': r'^8',
        '\u2079': r'^9',
        
        # Subscripts  
        '\u2080': r'_0',
        '\u2081': r'_1',
        '\u2082': r'_2',
        '\u2083': r'_3',
        '\u2084': r'_4',
        '\u2085': r'_5',
        '\u2086': r'_6',
        '\u2087': r'_7',
        '\u2088': r'_8',
        '\u2089': r'_9',
        
        # Other mathematical symbols
        '\u22c5': r'\cdot',       # center dot
        '\u00b7': r'\cdot',       # middle dot
        '\u2026': r'\ldots',      # ellipsis
        '\u2032': r"'",           # prime
        '\u2033': r"''",          # double prime
        '\u2034': r"'''",         # triple prime
        
        # Fractions
        '\u00bd': r'\frac{1}{2}',
        '\u2153': r'\frac{1}{3}',
        '\u2154': r'\frac{2}{3}',
        '\u00bc': r'\frac{1}{4}',
        '\u00be': r'\frac{3}{4}',
        
        # Special characters
        '\u00a0': r'~',           # non-breaking space
        '\u2013': r'--',          # en dash
        '\u2014': r'---',         # em dash
        '\u201c': r'``',          # left double quotation mark
        '\u201d': r"''",          # right double quotation mark
        '\u2018': r'`',           # left single quotation mark
        '\u2019': r"'",           # right single quotation mark
    }
    
    # Apply unicode conversions
    for unicode_char, latex_equiv in unicode_to_latex_map.items():
        text = text.replace(unicode_char, latex_equiv)
    
    return text

def format_equation(raw_str: str, eq_num: Optional[str] = None) -> str:
    """
    Format equation string for LaTeX with proper mathematical notation
    """
    if not raw_str.strip():
        return ""
    
    # Process unicode characters
    equation = process_unicode_text(raw_str)
    
    # Clean up spacing around mathematical operators
    equation = re.sub(r'\s*=\s*', ' = ', equation)
    equation = re.sub(r'\s*\+\s*', ' + ', equation)
    equation = re.sub(r'\s*-\s*', ' - ', equation)
    equation = re.sub(r'\s*\*\s*', ' \\cdot ', equation)
    
    # Handle subscripts and superscripts more intelligently
    # Convert patterns like "q t" to "q_t", "p t" to "p_t", etc.
    equation = re.sub(r'([a-zA-Z])\s+([a-zA-Z0-9])\b', r'\1_{\2}', equation)
    
    # Handle superscripts like "M −1" -> "M^{-1}"
    equation = re.sub(r'([a-zA-Z])\s*−\s*1\b', r'\1^{-1}', equation)
    equation = re.sub(r'([a-zA-Z])\s*-\s*1\b', r'\1^{-1}', equation)
    
    # Handle function notation like "V (q t)" -> "V(q_t)"
    equation = re.sub(r'([a-zA-Z])\s*\(\s*([^)]+)\s*\)', r'\1(\2)', equation)
    
    # Handle differential notation "d W t" -> "dW_t"
    equation = re.sub(r'd\s+([a-zA-Z])\s+([a-zA-Z0-9])', r'd\1_{\2}', equation)
    
    # Handle gradient notation "∇ q" -> "\\nabla_q"
    equation = re.sub(r'\\nabla\s+([a-zA-Z])', r'\\nabla_{\1}', equation)
    
    # Add spacing around commas in equations
    equation = re.sub(r',\s*', r', \\quad ', equation)
    
    # Handle dot products and multiplication
    equation = re.sub(r'\s+dt\b', r' \\, dt', equation)
    equation = re.sub(r'\s+dW', r' \\, dW', equation)
    
    # Clean up extra spaces
    equation = re.sub(r'\s+', ' ', equation).strip()
    
    # Format as LaTeX equation
    if eq_num and eq_num.strip():
        # Remove parentheses from equation number if present
        eq_num = eq_num.strip('()')
        return f"\\[\n{equation}\n\\tag{{{eq_num}}}\n\\]"
    else:
        return f"\\[\n{equation}\n\\]"

def process_text_block(text: str) -> str:
    """
    Process regular text blocks, handling unicode and LaTeX escaping
    """
    if not text or text.strip() == "EQUATION":
        return ""
    
    # Process unicode characters
    text = process_unicode_text(text)
    
    # Escape special LaTeX characters
    latex_special_chars = {
        '&': r'\&',
        '%': r'\%',
        '$': r'\$',
        '#': r'\#',
        '^': r'\textasciicircum{}',
        '_': r'\_',
        '{': r'\{',
        '}': r'\}',
        '~': r'\textasciitilde{}',
        '\\': r'\textbackslash{}',
    }
    
    # Don't escape characters that are already part of LaTeX commands
    for char, escape in latex_special_chars.items():
        if char not in ['\\']:  # Don't escape backslashes that might be LaTeX commands
            text = text.replace(char, escape)
    
    # Handle citations in brackets like [CV20], [PStoV21], etc.
    text = re.sub(r'\[([A-Z][A-Za-z0-9]+)\]', r'\\cite{\1}', text)
    
    return text

def extract_sections_and_content(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract and organize content from GROBID JSON structure
    """
    content_blocks = []
    
    # Add title if available
    if 'title' in data and data['title']:
        title = process_unicode_text(data['title'])
        content_blocks.append({
            'type': 'title',
            'content': title
        })
    
    # Add authors if available
    if 'authors' in data and data['authors']:
        authors = []
        for author in data['authors']:
            name_parts = []
            if author.get('first'):
                name_parts.append(author['first'])
            if author.get('middle'):
                name_parts.extend(author['middle'])
            if author.get('last'):
                name_parts.append(author['last'])
            if name_parts:
                authors.append(' '.join(name_parts))
        
        if authors:
            content_blocks.append({
                'type': 'authors',
                'content': ' and '.join(authors)
            })
    
    # Add abstract if available
    if 'pdf_parse' in data and 'abstract' in data['pdf_parse']:
        for abstract_block in data['pdf_parse']['abstract']:
            if abstract_block.get('text'):
                content_blocks.append({
                    'type': 'abstract',
                    'content': process_text_block(abstract_block['text']),
                    'section': abstract_block.get('section', 'Abstract')
                })
    
    # Process body text
    if 'pdf_parse' in data and 'body_text' in data['pdf_parse']:
        current_section = None
        
        for block in data['pdf_parse']['body_text']:
            # Handle section changes
            if block.get('section') and block['section'] != current_section:
                current_section = block['section']
                content_blocks.append({
                    'type': 'section',
                    'content': process_text_block(current_section),
                    'section_num': block.get('sec_num')
                })
            
            # Handle equations
            if 'eq_spans' in block and block['eq_spans']:
                for eq in block['eq_spans']:
                    raw_str = eq.get('raw_str', '')
                    eq_num = eq.get('eq_num', '')
                    if raw_str.strip():
                        formatted_eq = format_equation(raw_str, eq_num)
                        content_blocks.append({
                            'type': 'equation',
                            'content': formatted_eq,
                            'equation_number': eq_num,
                            'section': current_section
                        })
            
            # Handle regular text
            if 'text' in block and block['text'] and block['text'].strip() != "EQUATION":
                processed_text = process_text_block(block['text'])
                if processed_text.strip():
                    content_blocks.append({
                        'type': 'text',
                        'content': processed_text,
                        'section': current_section
                    })
    
    return content_blocks

def generate_latex_document(content_blocks: List[Dict[str, Any]], 
                          output_file: str = "output.tex",
                          document_class: str = "article",
                          include_packages: List[str] = None) -> None:
    """
    Generate complete LaTeX document from content blocks
    """
    if include_packages is None:
        include_packages = [
            "amsmath",
            "amssymb", 
            "amsfonts",
            "mathtools",
            "unicode-math",
            "fontspec",
            "geometry",
            "hyperref",
            "natbib"
        ]
    
    latex_lines = [
        f"\\documentclass{{{document_class}}}",
        ""
    ]
    
    # Add packages
    for package in include_packages:
        latex_lines.append(f"\\usepackage{{{package}}}")
    
    latex_lines.extend([
        "",
        "% Set page geometry",
        "\\geometry{margin=1in}",
        "",
        "% Configure hyperref",
        "\\hypersetup{",
        "    colorlinks=true,",
        "    linkcolor=blue,",
        "    citecolor=red,",
        "    urlcolor=blue",
        "}",
        "",
        "\\begin{document}",
        ""
    ])
    
    # Process content blocks
    for block in content_blocks:
        block_type = block['type']
        content = block['content']
        
        if block_type == 'title':
            latex_lines.extend([
                f"\\title{{{content}}}",
                "\\maketitle",
                ""
            ])
        
        elif block_type == 'authors':
            latex_lines.extend([
                f"\\author{{{content}}}",
                ""
            ])
        
        elif block_type == 'abstract':
            latex_lines.extend([
                "\\begin{abstract}",
                content,
                "\\end{abstract}",
                ""
            ])
        
        elif block_type == 'section':
            section_num = block.get('section_num', '')
            if section_num:
                latex_lines.append(f"\\section{{{content}}} % Section {section_num}")
            else:
                latex_lines.append(f"\\section{{{content}}}")
            latex_lines.append("")
        
        elif block_type == 'equation':
            latex_lines.extend([
                content,
                ""
            ])
        
        elif block_type == 'text':
            latex_lines.extend([
                f"\\noindent {content}",
                ""
            ])
    
    latex_lines.extend([
        "\\end{document}"
    ])
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(latex_lines))

def convert_grobid_json_to_latex(json_path: str, 
                                output_path: str = None,
                                document_class: str = "article",
                                custom_packages: List[str] = None) -> str:
    """
    Main function to convert GROBID JSON to LaTeX
    
    Args:
        json_path: Path to the GROBID JSON file
        output_path: Output LaTeX file path (auto-generated if None)
        document_class: LaTeX document class to use
        custom_packages: Additional LaTeX packages to include
        
    Returns:
        Path to the generated LaTeX file
    """
    
    # Generate output path if not provided
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(json_path))[0]
        output_path = f"{base_name}_converted.tex"
    
    # Load JSON data
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        raise ValueError(f"Error loading JSON file: {e}")
    
    # Extract content
    content_blocks = extract_sections_and_content(data)
    
    # Generate LaTeX document
    generate_latex_document(
        content_blocks, 
        output_path, 
        document_class, 
        custom_packages
    )
    
    print(f"✅ Conversion completed: {output_path}")
    print(f"📊 Processed {len(content_blocks)} content blocks")
    
    # Count different types of content
    type_counts = {}
    for block in content_blocks:
        block_type = block['type']
        type_counts[block_type] = type_counts.get(block_type, 0) + 1
    
    print("📈 Content breakdown:")
    for content_type, count in type_counts.items():
        print(f"   - {content_type}: {count}")
    
    return output_path

def batch_convert_json_files(input_directory: str, 
                           output_directory: str = None,
                           file_pattern: str = "*.json") -> List[str]:
    """
    Convert multiple JSON files in a directory
    
    Args:
        input_directory: Directory containing JSON files
        output_directory: Output directory for LaTeX files
        file_pattern: Pattern to match JSON files
        
    Returns:
        List of generated LaTeX file paths
    """
    import glob
    
    if output_directory is None:
        output_directory = input_directory
    
    # Create output directory if it doesn't exist
    os.makedirs(output_directory, exist_ok=True)
    
    # Find JSON files
    json_files = glob.glob(os.path.join(input_directory, file_pattern))
    
    converted_files = []
    
    for json_file in json_files:
        try:
            base_name = os.path.splitext(os.path.basename(json_file))[0]
            output_file = os.path.join(output_directory, f"{base_name}_converted.tex")
            
            convert_grobid_json_to_latex(json_file, output_file)
            converted_files.append(output_file)
            
        except Exception as e:
            print(f"❌ Error converting {json_file}: {e}")
    
    print(f"\n🎉 Batch conversion completed: {len(converted_files)} files converted")
    return converted_files

# Example usage and testing
if __name__ == "__main__":
    # Example: Convert a single file
    try:
        # Convert the example file
        json_file = "mcf_analytical_presentation.json"
        if os.path.exists(json_file):
            output_file = convert_grobid_json_to_latex(
                json_file, 
                "mcf_analytical_presentation_converted.tex"
            )
            print(f"📄 Generated: {output_file}")
        else:
            print(f"❌ File not found: {json_file}")
            
        # Example: Batch convert all JSON files in output_dir
        if os.path.exists("output_dir"):
            print("\n🔄 Starting batch conversion...")
            batch_convert_json_files("output_dir", "latex_output")
    
    except Exception as e:
        print(f"❌ Error: {e}")
