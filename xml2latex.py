import xml.etree.ElementTree as ET
import re
from mathml2latex import convert as mathml2latex
# 注意：需要安装 mathml2latex 库，可以使用 `pip install mathml2latex`

# TEI & MathML 命名空间
NS = {
    'tei': 'http://www.tei-c.org/ns/1.0',
    'm': 'http://www.w3.org/1998/Math/MathML'
}

def extract_formulas(tei_file, output_file="output_formulas.tex"):
    tree = ET.parse(tei_file)
    root = tree.getroot()

    formulas = []
    for formula in root.findall(".//tei:formula", NS):
        # 1. 尝试提取 MathML
        mathml = formula.find(".//m:math", NS)
        if mathml is not None:
            mathml_str = ET.tostring(mathml, encoding="unicode")
            try:
                latex = mathml2latex(mathml_str)
            except Exception as e:
                print(f"[⚠️] MathML 转换失败: {e}")
                latex = "<!-- MathML 解析失败 -->"
        else:
            # 2. 提取文本内容（可能是伪 LaTeX 或公式文本）
            text_content = "".join(formula.itertext()).strip()
            latex = try_clean_pseudolatex(text_content)
        formulas.append(latex)

    # 写入输出 LaTeX 文件
    with open(output_file, "w", encoding="utf-8") as f:
        for i, latex in enumerate(formulas):
            f.write(f"% --- Formula {i+1} ---\n")
            f.write(f"$$ {latex} $$\n\n")
    print(f"✅ 成功提取 {len(formulas)} 条公式到 {output_file}")

def try_clean_pseudolatex(text):
    # 基本清洗逻辑，可以扩展为正则修复
    text = re.sub(r"\s+", " ", text)
    # 替换常见符号（示例）
    replacements = {
        "dX t": "dX_t",
        "dY t": "dY_t",
        "X t": "X_t",
        "Y t": "Y_t",
        "f (": "f(",
        "t Rm": "t \\in \\mathbb{R}^m",
        "t Rn": "t \\in \\mathbb{R}^n",
    }
    for k, v in replacements.items():
        text = text.replace(k, v)
    return text

# 用法示例
if __name__ == "__main__":
    extract_formulas("mcf_analytical_presentation_original.tei.xml", "formulas_output.tex")
